import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { VercelRequest, VercelResponse } from '@vercel/node';

// Load environment variables BEFORE importing API handlers
dotenv.config();

// Ensure environment variables are available
if (!process.env.OPENAI_API_KEY || !process.env.FIRECRAWL_API_KEY) {
  console.error('⚠️  Missing required environment variables!');
  console.error('   OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? '✅' : '❌ Missing');
  console.error('   FIRECRAWL_API_KEY:', process.env.FIRECRAWL_API_KEY ? '✅' : '❌ Missing');
  process.exit(1);
}

// Import the actual API handlers
import analyzeWebsiteComprehensiveHandler from './api/analyze-website-comprehensive-local';
import analyzeWebsiteHandler from './api/analyze-website';
import websiteMetadataHandler from './api/website-metadata';

const app = express();
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Create adapter for Vercel serverless functions
const adaptHandler = (handler: (req: VercelRequest, res: VercelResponse) => Promise<any>) => {
  return async (req: express.Request, res: express.Response) => {
    try {
      // Create mock VercelRequest
      const vercelReq = {
        ...req,
        query: req.query as any,
        cookies: req.cookies || {},
        body: req.body,
      } as VercelRequest;

      // Create mock VercelResponse
      const vercelRes = {
        status: (code: number) => {
          res.status(code);
          return vercelRes;
        },
        json: (data: any) => {
          res.json(data);
          return vercelRes;
        },
        send: (data: any) => {
          res.send(data);
          return vercelRes;
        },
        setHeader: (name: string, value: string) => {
          res.setHeader(name, value);
          return vercelRes;
        },
      } as VercelResponse;

      // Call the handler
      await handler(vercelReq, vercelRes);
    } catch (error: any) {
      console.error('Handler error:', error);
      res.status(500).json({ 
        error: 'Internal server error', 
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  };
};

// Set up API routes
app.post('/api/analyze-website-comprehensive', adaptHandler(analyzeWebsiteComprehensiveHandler));
app.post('/api/analyze-website', adaptHandler(analyzeWebsiteHandler));
app.post('/api/website-metadata', adaptHandler(websiteMetadataHandler));

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    env: {
      hasOpenAI: !!process.env.OPENAI_API_KEY,
      hasFirecrawl: !!process.env.FIRECRAWL_API_KEY,
    }
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Express error:', err);
  res.status(500).json({
    error: 'Server error',
    message: err.message
  });
});

const PORT = process.env.API_PORT || 3000;
app.listen(PORT, () => {
  console.log(`\n🚀 Local API server running on http://localhost:${PORT}`);
  console.log('\nAvailable endpoints:');
  console.log('  POST /api/analyze-website-comprehensive');
  console.log('  POST /api/analyze-website');
  console.log('  POST /api/website-metadata');
  console.log('  GET  /api/health\n');
  console.log('Environment check:');
  console.log(`  OpenAI API Key: ${process.env.OPENAI_API_KEY ? '✅ Configured' : '❌ Missing'}`);
  console.log(`  Firecrawl API Key: ${process.env.FIRECRAWL_API_KEY ? '✅ Configured' : '❌ Missing'}\n`);
  
  if (!process.env.OPENAI_API_KEY || !process.env.FIRECRAWL_API_KEY) {
    console.warn('⚠️  Warning: Missing API keys. The API endpoints will not work properly.');
    console.warn('   Please ensure your .env file contains OPENAI_API_KEY and FIRECRAWL_API_KEY\n');
  }
});