# Google Calendar + Stripe Payment Link Setup Guide

This guide explains how to set up Google Calendar for FREE appointment scheduling, followed by a separate Stripe payment link with proper VAT handling.

## Prerequisites

1. Google Workspace account (Business Standard or higher)
2. Stripe account with Danish business registration
3. VAT registration number

## Step 1: Create a Stripe Payment Link

1. Log in to your Stripe Dashboard
2. Go to **Payment Links** → **New**
3. Add your product:
   - Name: "Marketing Therapy Session - 2 timer"
   - Price: 7,187.50 DKK
   - Tax: Enable "Collect tax" and select Denmark (25%)
   - Make sure the total shows 7,187.50 DKK (inclusive of VAT)

4. Configure customer information:
   - Enable "Collect email"
   - Enable "Collect name"
   - Enable "Collect billing address"

5. Configure after payment:
   - Success page: Your website success URL
   - Or use <PERSON><PERSON>'s default success page

6. Click "Create link" and copy the URL

## Step 2: Set Up Google Calendar Appointment Scheduling (FREE)

1. Open Google Calendar
2. Click **Create** → **Appointment schedule**
3. Configure your appointment:
   - Title: "Marketing Therapy Session"
   - Duration: 2 hours
   - Location: Google Meet (or custom)
   - Set your availability

4. **IMPORTANT**: Do NOT connect Stripe - keep it as a free appointment

5. Configure booking settings:
   - Set buffer time between appointments
   - Set how far in advance people can book
   - Add any questionnaire fields if needed

## Step 3: Get Your Links

1. Open your appointment schedule in Google Calendar
2. Click **Share** → **Website embed**
3. Copy the appointment schedule URL
4. The URL format will be: `https://calendar.google.com/calendar/appointments/schedules/YOUR_SCHEDULE_ID`

## Step 4: Configure the Website

Update your `.env` file:

```env
# Google Calendar Integration (FREE appointments)
VITE_GOOGLE_CALENDAR_APPOINTMENT_URL=https://calendar.google.com/calendar/appointments/schedules/YOUR_SCHEDULE_ID
VITE_GOOGLE_CALENDAR_SCHEDULE_ID=YOUR_SCHEDULE_ID

# Stripe Payment Link (with VAT)
VITE_STRIPE_PAYMENT_LINK=https://book.stripe.com/YOUR_PAYMENT_LINK_ID
```

## How It Works

1. **User Flow**:
   - User fills out the booking form with their information
   - On step 3, they see the Google Calendar embed
   - They select a FREE time slot in Google Calendar
   - After booking, they're automatically redirected to Stripe payment link
   - Stripe payment link is pre-filled with their email
   - User pays 7,187.50 kr (including 25% VAT)
   - They receive separate confirmations from Google and Stripe

2. **VAT Handling**:
   - Stripe payment link includes Danish VAT (25%)
   - Price is shown as 7,187.50 DKK total
   - VAT breakdown: 5,750 kr + 1,437.50 kr VAT
   - Proper invoices are generated with all VAT details

3. **Confirmations**:
   - Google Calendar sends appointment confirmation immediately
   - Stripe sends payment receipt after payment
   - Both emails contain relevant information
   - Optional: Set up Brevo for unified follow-up emails

## Testing

1. Use Stripe test mode first:
   - Use test card: 4242 4242 4242 4242
   - Any future date and any CVC

2. Test VAT scenarios:
   - Danish customer: Should see 25% VAT
   - EU business with VAT number: Should see reverse charge
   - Non-EU customer: Should see 0% VAT

## Troubleshooting

- **"Can't connect Stripe"**: Ensure you have a Google Workspace eligible plan
- **VAT not showing**: Check Stripe Tax is enabled and registration is active
- **Wrong amounts**: Verify product price is set as "inclusive of tax"

## Manual Verification Process

Since booking and payment are separate:

1. Check both Google Calendar and Stripe Dashboard
2. Match bookings with payments using email/name
3. Send manual confirmation when both are complete
4. Consider setting up webhooks to automate this

## Benefits of This Approach

- ✅ Full control over VAT settings in Stripe
- ✅ Works with any Google Workspace plan
- ✅ Clear separation of booking and payment
- ✅ Easy to test and debug
- ✅ No complex integrations to maintain