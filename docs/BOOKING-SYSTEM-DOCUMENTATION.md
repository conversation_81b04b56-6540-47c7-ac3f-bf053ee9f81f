# Booking System & Google Calendar Integration Documentation

## Overview
This document describes the complete booking system implementation for the Marketing Therapy Journey website, including Google Calendar integration, email notifications, and real-time availability checking.

## System Architecture

### Components Overview
```
User Interface (React)
    ↓
Booking Forms (IntroBooking/SimplifiedBooking)
    ↓
API Endpoints (Vercel Functions)
    ↓
Google Calendar API
    ↓
Email Service (Resend)
```

## Key Features Implemented

### 1. Real-Time Availability Display
- **AvailabilityWidget Component** (`/src/components/AvailabilityWidget.tsx`)
  - Shows floating widget with current availability
  - Fetches data from `/api/availability-summary`
  - Updates every 60 seconds
  - Shows after 20 seconds on page
  - Can be minimized/dismissed
  - Non-intrusive design with subtle animations

### 2. Dynamic Booking Form
- **IntroBooking Page** (`/src/pages/IntroBooking.tsx`)
  - Generates future dates dynamically (next 7 business days)
  - Skips weekends automatically
  - Fetches real availability from `/api/available-slots`
  - Shows loading states while fetching
  - Danish date formatting (e.g., "torsdag 7. november")
  - Form validation for all required fields

### 3. Google Calendar Integration

#### API Endpoints

**`/api/available-slots.ts`**
- Checks Google Calendar for busy times
- Returns available time slots for a specific date
- Business hours: 9:00-17:00 (configurable)
- Session duration: 30 minutes for intro calls
- Filters out:
  - Past times
  - Times less than 18 hours away
  - Weekends (unless configured)
  - Times that conflict with existing calendar events

**`/api/availability-summary.ts`**
- Provides aggregated availability data
- Returns:
  - Number of slots available this week
  - Number of slots available next week
  - Next available time slot
  - Total slots available this month

**`/api/create-calendar-event.ts`**
- Creates calendar events when booking is confirmed
- Adds event with:
  - Customer name and email
  - Session goals/challenges
  - 30-minute duration
  - Sends calendar invites to both parties
- Protected by internal API key authentication

## Configuration & Environment Variables

### Required Environment Variables

```bash
# Google Calendar Service Account (for API access)
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"

# Your Google Calendar ID
GOOGLE_CALENDAR_ID=<EMAIL>
# Can be your email or a calendar ID like: <EMAIL>

# Internal API Security
INTERNAL_API_KEY=your-secure-random-key-here
VITE_INTERNAL_API_KEY=same-key-as-above  # For frontend access

# Email Service (Resend)
RESEND_API_KEY=your-resend-api-key

# Optional: Slack notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/xxx
```

### Setting Up Google Calendar Integration

1. **Create a Google Cloud Project**
   - Go to https://console.cloud.google.com
   - Create a new project or select existing
   - Enable Google Calendar API

2. **Create Service Account**
   - Go to "IAM & Admin" → "Service Accounts"
   - Create new service account
   - Download JSON key file
   - Extract email and private key for environment variables

3. **Share Calendar with Service Account**
   - Open Google Calendar settings
   - Find the calendar you want to use
   - Share it with the service account email
   - Give it "Make changes to events" permission

4. **Get Calendar ID**
   - In Google Calendar settings
   - Find "Integrate calendar" section
   - Copy the Calendar ID

## Booking Flow

### User Journey

1. **User visits website**
   - Availability widget appears after 20 seconds
   - Shows current availability stats

2. **User clicks "Lad os snakke" (Let's talk)**
   - Navigates to `/booking/intro`
   - Form loads with dynamic future dates
   - Real availability fetched from calendar

3. **User fills form**
   - Name, email, phone (optional)
   - Describes their challenge
   - Selects available time slot

4. **Form submission**
   ```javascript
   // Process flow:
   1. Validate all fields
   2. Parse selected date/time
   3. Create calendar event via API
   4. Send confirmation email
   5. Show success message
   6. Redirect to success page
   ```

5. **Backend processing**
   - Calendar event created in your Google Calendar
   - Email confirmation sent to customer
   - Calendar invites sent to both parties
   - Slack notification (if configured)

## Code Structure

### Frontend Components

```
/src/components/
├── AvailabilityWidget.tsx    # Floating availability display
├── Hero.tsx                   # Updated with casual CTAs
├── SolutionSection.tsx        # Service overview with booking
├── FinalCTA.tsx              # Bottom CTA section
└── booking/
    ├── SimplifiedStepOne.tsx  # Multi-step booking form
    └── IntroBooking.tsx       # Quick intro call booking

/src/pages/
├── IntroBooking.tsx          # Main intro booking page
├── SimplifiedBooking.tsx     # Full session booking
├── IntroBookingSuccess.tsx   # Success confirmation
└── BookingSuccess.tsx        # Full booking success
```

### API Endpoints

```
/api/
├── available-slots.ts         # Check calendar availability
├── availability-summary.ts    # Aggregate availability data
├── create-calendar-event.ts   # Create Google Calendar events
├── send-booking-email.ts      # Send email confirmations
└── booking.ts                 # Main booking handler
```

### Translation Files

```
/public/locales/da/
├── home.json                  # Homepage text (casualized)
├── booking.json               # Booking form text
├── common.json               # Shared UI text
└── about.json                # About page text
```

## Key Implementation Details

### Date Parsing (Danish to ISO)
```javascript
// Convert "torsdag 7. november" to ISO date
const monthMap = {
  'januar': 0, 'februar': 1, 'marts': 2, 'april': 3,
  'maj': 4, 'juni': 5, 'juli': 6, 'august': 7,
  'september': 8, 'oktober': 9, 'november': 10, 'december': 11
};

const dateMatch = selectedSlot.date.match(/(\d+)\.\s+(\w+)/);
const day = parseInt(dateMatch[1]);
const monthName = dateMatch[2].toLowerCase();
const month = monthMap[monthName];
```

### Availability Checking
```javascript
// Business rules
const BUSINESS_START_HOUR = 9;
const BUSINESS_END_HOUR = 17;
const SESSION_DURATION_MINUTES = 30;

// Time slots
const WEEKDAY_TIME_SLOTS = [
  '09:00', '10:00', '11:00', '12:00', 
  '13:00', '14:00', '15:00', '16:00'
];
```

### Security
- Internal API key authentication for calendar operations
- CORS headers configured for API endpoints
- Environment variables for sensitive data
- No client-side calendar credentials

## Conversion Optimizations Applied

### 1. Casual, Authentic Messaging
- Changed from "Book 10 min diagnosis →" to "Lad os snakke"
- Removed aggressive urgency (~~"🔥 ONLY 3 SPOTS LEFT!"~~)
- Made availability informational, not pushy
- Simplified trust signals

### 2. Real Availability (Not Fake Scarcity)
- Actually checks Google Calendar
- Shows true available slots
- Updates in real-time
- No fake countdown timers

### 3. Smooth User Experience
- Loading states while fetching
- Error handling with fallbacks
- Mobile-responsive design
- Neo-brutalist aesthetic maintained

## Testing the System

### Local Development
```bash
# Start dev server
npm run dev

# Test booking flow
1. Navigate to /booking/intro
2. Fill out form
3. Check console for debug logs
4. Verify calendar event creation
```

### Production Checklist
- [ ] All environment variables set in Vercel
- [ ] Google Calendar shared with service account
- [ ] Internal API key configured
- [ ] Email service (Resend) configured
- [ ] Calendar ID points to correct calendar
- [ ] Test booking creates real calendar event

## Common Issues & Solutions

### Issue: "Prøv igen eller send en mail"
**Cause**: Time slot selection mismatch
**Solution**: Fixed by using consistent format `${date}-${time}`

### Issue: No available times showing
**Cause**: Restrictive business hours or wrong calendar
**Solution**: 
- Extended hours to 9:00-17:00
- Changed to 30-minute sessions
- Verify GOOGLE_CALENDAR_ID is correct

### Issue: Calendar events not created
**Cause**: Missing API key or permissions
**Solution**:
- Add INTERNAL_API_KEY to Vercel
- Ensure service account has calendar access
- Check API endpoint logs in Vercel

## Future Enhancements

1. **Payment Integration**
   - Stripe checkout for paid sessions
   - Automatic invoice generation

2. **Automated Reminders**
   - Email reminders 24 hours before
   - SMS notifications

3. **Rescheduling**
   - Allow customers to reschedule
   - Automatic calendar updates

4. **Analytics**
   - Track conversion rates
   - Monitor availability vs. bookings
   - A/B test different messaging

## Support & Maintenance

For issues or questions:
- Check Vercel function logs for API errors
- Verify environment variables are set
- Test with console logging enabled
- Email: <EMAIL>

---

*This system was built with a focus on authenticity and user experience, avoiding dark patterns while maintaining effective conversion optimization.*