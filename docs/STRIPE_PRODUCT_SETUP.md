# Stripe Product Setup Guide

## Creating a Product with Proper VAT Handling

To ensure proper Danish VAT handling, follow these steps to create your product in Stripe:

### 1. Create Product in Stripe Dashboard

1. Go to [Stripe Dashboard > Products](https://dashboard.stripe.com/products)
2. Click "Add product"
3. Fill in the details:
   - **Name**: Marketing Terapi Session - 60 minutter
   - **Description**: Personlig marketing konsultation med Asger Teglgaard
   - **Image**: Upload your service image (optional)

### 2. Set Up Pricing

1. Add a price:
   - **Currency**: DKK
   - **Price**: 1500.00 (base price without VAT)
   - **Pricing model**: One-time
   
2. Click "Save product"

### 3. Configure Tax Settings

1. Go to [Stripe Dashboard > Settings > Tax](https://dashboard.stripe.com/settings/tax)
2. Enable "Automatic tax calculation"
3. Add your tax registrations:
   - **Country**: Denmark
   - **Registration number**: Your CVR number
   - **Tax type**: VAT
   
4. Configure product tax behavior:
   - Go back to your product
   - Edit the product
   - Under "Tax" section:
     - **Tax behavior**: Exclusive (VAT will be added on top)
     - **Tax code**: Select appropriate service tax code (e.g., "Consulting services")

### 4. Get Your Price ID

1. In your product page, find the price you created
2. Copy the Price ID (starts with `price_`)
3. Update your `.env` file:
   ```
   STRIPE_MARKETING_THERAPY_PRICE_ID=price_1234567890abcdef
   ```

### 5. Test the Setup

1. Create a test payment link from Stripe Dashboard
2. Verify that:
   - Base price shows as 1500 DKK
   - VAT (25%) is added automatically = 375 DKK
   - Total shows as 1875 DKK
   - Tax ID collection form appears

## Benefits of Using Pre-configured Products

✅ **Proper VAT Calculation**: Stripe automatically calculates and displays VAT
✅ **Tax Compliance**: Correct tax codes and reporting
✅ **Invoice Generation**: Professional invoices with proper tax breakdown
✅ **Reusability**: Same product can be used in payment links, invoices, etc.
✅ **Analytics**: Better reporting in Stripe Dashboard

## Alternative: Tax-Inclusive Pricing

If you prefer to show a fixed price including VAT:

1. Set price to 1875 DKK (1500 + 375 VAT)
2. Change tax behavior to "Inclusive"
3. Stripe will calculate and show the VAT portion on invoices

## Webhook Handling

The webhook will now receive the actual amount paid including tax:
- `session.amount_subtotal`: 150000 (1500 DKK in øre)
- `session.amount_total`: 187500 (1875 DKK in øre)
- `session.total_details.amount_tax`: 37500 (375 DKK in øre)

This ensures proper tracking of revenue and tax collected.