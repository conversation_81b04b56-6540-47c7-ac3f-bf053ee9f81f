# Testing the Google Calendar + Stripe Booking Flow

## Overview
This guide explains how to test the booking flow locally and work around CORS issues with Google Calendar embeds.

## Environment Setup

### 1. Create `.env.local` file
Copy `.env.local.example` to `.env.local` and add your values:

```bash
cp .env.local.example .env.local
```

### 2. Get your Google Calendar Appointment URL

1. Go to Google Calendar
2. Click Create → Appointment schedule
3. Set up your appointment details
4. Click on the appointment schedule
5. Click "Share" → "Get link"
6. Copy the URL (it will look like: `https://calendar.google.com/calendar/appointments/schedules/AcZssZ2...`)

### 3. Update `.env.local`

```env
# Your actual appointment schedule URL
VITE_GOOGLE_CALENDAR_APPOINTMENT_URL=https://calendar.google.com/calendar/appointments/schedules/YOUR_ACTUAL_ID
VITE_GOOGLE_CALENDAR_SCHEDULE_ID=YOUR_ACTUAL_ID
```

## Testing Approaches

### Option 1: Test with CORS Disabled (Chrome)

**Windows:**
```bash
"C:\Program Files\Google\Chrome\Application\chrome.exe" --disable-web-security --user-data-dir="C:\temp\chrome_test"
```

**Mac:**
```bash
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_test" --disable-web-security --disable-site-isolation-trials
```

**Linux:**
```bash
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_test"
```

⚠️ **Security Warning**: Only use this for testing. Never browse other sites with security disabled.

### Option 2: Use a Proxy (Recommended for Development)

Add to `vite.config.ts`:

```typescript
export default defineConfig({
  server: {
    proxy: {
      '/calendar-proxy': {
        target: 'https://calendar.google.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/calendar-proxy/, '/calendar')
      }
    }
  }
})
```

Then update the embed URL in development to use the proxy.

### Option 3: Deploy to Staging

1. Deploy to a staging environment (Netlify, Vercel, etc.)
2. Add your staging domain to Google Workspace allowed domains
3. Test with the real embed

### Option 4: Create a Mock Component (Best for Local Development)

Create `src/components/booking/GoogleCalendarEmbedMock.tsx` with the mock component from this guide, then conditionally use it in development:

```typescript
const CalendarComponent = import.meta.env.DEV 
  ? GoogleCalendarEmbedMock 
  : GoogleCalendarEmbed;
```

## Testing the Full Flow

### 1. Local Testing Steps
1. Start the dev server: `npm run dev`
2. Navigate to `/booking`
3. Fill out steps 1 and 2 of the form
4. On step 3, you'll see either:
   - The mock calendar (if using Option 4)
   - A CORS error (if not using any workaround)
   - The real calendar (if using Options 1-3)

### 2. What to Test
- [ ] Form data is saved between steps
- [ ] Validation works on all fields
- [ ] Terms must be accepted before calendar shows
- [ ] Calendar embed loads (or mock works)
- [ ] Price shows correctly (7,187.50 kr inkl. moms)
- [ ] Navigation works between steps

### 3. Production Testing
Once deployed:
1. Test the real Google Calendar embed
2. Click a time slot
3. Verify redirect to Stripe Checkout
4. Verify VAT is calculated correctly
5. Complete test payment
6. Verify confirmations are sent

## Common Issues

### "Refused to display in a frame"
This is the CORS issue. Use one of the testing approaches above.

### Calendar not loading
- Check your appointment schedule is published
- Verify the URL in `.env.local` is correct
- Make sure you're using `https://` in production

### Stripe not working
- Ensure Stripe is connected in Google Calendar settings
- Verify payment is required on the appointment schedule
- Check Stripe Tax is configured

## Mock Component for Testing

Save this as `src/components/booking/GoogleCalendarEmbedMock.tsx` for local testing without CORS issues.