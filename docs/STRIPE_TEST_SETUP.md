# Stripe Test Environment Setup

## 1. Get Your Test API Keys

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Toggle to **Test mode** (switch in the top right)
3. Go to [Developers > API keys](https://dashboard.stripe.com/test/apikeys)
4. Copy your test keys:
   - **Publishable key**: `pk_test_...`
   - **Secret key**: `sk_test_...`

## 2. Create Test Product

1. In test mode, go to [Products](https://dashboard.stripe.com/test/products)
2. Create the same product:
   - **Name**: Marketing Terapi Session - 60 minutter
   - **Price**: 1500 DKK
   - **Tax code**: General - Services
3. Copy the test Price ID: `price_...`

## 3. Update .env.development

Replace the placeholders in `.env.development`:

```bash
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_TEST_KEY
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_TEST_KEY
STRIPE_MARKETING_THERAPY_PRICE_ID=price_YOUR_TEST_PRICE_ID
```

## 4. Set Up Test Webhook (Optional)

For local testing with webhooks:

1. Install Stripe CLI: `brew install stripe/stripe-cli/stripe`
2. Login: `stripe login`
3. Forward webhooks to local: 
   ```bash
   stripe listen --forward-to localhost:5173/api/stripe-webhook
   ```
4. Copy the webhook signing secret: `whsec_...`
5. Update `STRIPE_WEBHOOK_SECRET` in `.env.development`

## 5. Test Card Numbers

Use these test cards in Stripe Checkout:

- **Success**: `4242 4242 4242 4242`
- **Requires authentication**: `4000 0025 0000 3155`
- **Declined**: `4000 0000 0000 9995`

All test cards use:
- Any future expiry date
- Any 3-digit CVC
- Any 5-digit postal code

## 6. Test VAT Numbers

For testing EU VAT validation:
- **Valid Danish VAT**: `DK12345678`
- **Valid German VAT**: `DE123456789`

## 7. Running in Development

```bash
# Start with test environment
npm run dev

# The app will automatically use .env.development
```

## 8. Verifying Test Mode

You should see:
- Stripe Checkout shows "TEST MODE" banner
- No real charges are made
- Webhook events show in Stripe CLI

## Common Issues

### "Invalid API Key"
- Make sure you're using test keys (start with `sk_test_` and `pk_test_`)
- Check you haven't mixed live and test keys

### "Product not found"
- Create the product in test mode (not live mode)
- Use the test Price ID

### VAT Not Showing
- Enable tax in test mode: Settings > Tax > Enable automatic tax
- Add test tax registration if needed