## Time-Based Engagement Modal/Slide-in Component

### Feature Overview
Create a React component that displays a modal or slide-in notification after a user has been on the website for 10 seconds. The component should include a live counter showing how long the user has been on the page.

### Core Requirements

**1. Timer Functionality**
- Start a timer when the component mounts (page loads)
- After exactly 10 seconds, trigger the display of a modal/slide-in
- Continue counting time even after the modal appears

**2. Display Options**
- Support two display modes: full-screen modal OR corner slide-in
- Modal: Centered overlay with semi-transparent background
- Slide-in: Appears from bottom-right corner of the screen

**3. Counter Display**
- Show text: "You've been on our website for [X] seconds"
- Update the counter in real-time (11, 12, 13, etc.)
- Display should be prominent (large font size)

**4. User Interactions**
- Include a close button (X) to dismiss the component
- Add a primary CTA button (e.g., "Sign Up")
- Add a secondary button (e.g., "Maybe Later")
- Clicking outside the modal should close it (for modal version)

### Technical Specifications

**State Management**
- Track seconds elapsed since page load
- Track modal visibility (shown/hidden)
- Track display type (modal vs slide-in)

**Styling Requirements**
- Responsive design that works on mobile and desktop
- Smooth entrance animations (fade-in for modal, slide-in from right)
- Modern, clean design with proper spacing
- Z-index management to ensure it appears above other content

**Performance Considerations**
- Clean up timers on component unmount
- Prevent memory leaks
- Lightweight implementation

### Optional Enhancements
1. Add localStorage to remember if user has already seen it
2. Make the trigger time configurable (default 10 seconds)
3. Add form inputs for email capture
4. Include different messages based on page/route
5. Add exit intent detection as an alternative trigger
6. A/B testing capability between modal and slide-in
7. Analytics tracking for open/close/CTA clicks

### Example User Flow
1. User lands on website
2. Timer starts automatically
3. User browses the page
4. At 10 seconds, modal/slide-in appears with animation
5. Counter shows "You've been on our website for 10 seconds"
6. Counter continues updating (11, 12, 13...)
7. User can either:
   - Click "Sign Up" → trigger sign-up flow
   - Click "Maybe Later" or X → close the component
   - Continue browsing with slide-in visible (if using slide-in mode)