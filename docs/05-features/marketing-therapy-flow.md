# Marketing Therapy Booking Flow

## Overview
The Marketing Therapy booking flow is a multi-step form that collects detailed information from potential clients, processes payment via Stripe, and schedules appointments through Cal.com integration.

## Current Implementation Status

### Completed Features

#### 1. Hybrid Stripe + Cal.com Integration
- **Problem Solved**: Cal.com's native Stripe integration doesn't support Danish VAT properly
- **Solution**: Implemented a hybrid approach:
  - Custom Stripe checkout for payment collection (with proper VAT)
  - Cal.com for scheduling only (no payment)
  - Payment verification endpoint to bridge the two systems

#### 2. Full Danish Translation
- All UI components translated to Danish
- API error messages and responses in Danish
- Form validation messages in Danish
- Pricing updated from $197 to 1500 DKK throughout

#### 3. Development Environment Fix
- Fixed mock checkout issue in development
- Now uses real Stripe test mode when test keys are configured
- Proper environment variable handling for VITE_STRIPE_PUBLISHABLE_KEY

### Technical Architecture

#### Components Structure
```
src/
├── pages/
│   ├── Booking.tsx          # Main booking flow orchestrator
│   └── BookingSuccess.tsx   # Success page with Cal.com embed
├── components/booking/
│   ├── StepOne.tsx         # Basic information
│   ├── StepTwo.tsx         # Business context
│   ├── StepThree.tsx       # Marketing details
│   ├── StepFour.tsx        # Session preparation
│   ├── StepFive.tsx        # AI summary review
│   ├── BookingInfo.tsx     # Service information sidebar
│   └── AISummaryEditor.tsx # Summary editor component
├── contexts/
│   └── FormContext.tsx     # Form state management
└── services/
    ├── stripeCheckoutService.ts
    └── aiAnalysisService.ts
```

#### API Endpoints
```
api/
├── create-consultation-checkout.ts  # Creates Stripe checkout session
├── verify-payment-session.ts       # Verifies payment completion
├── generate-comprehensive-summary.ts # Generates AI summary
├── save-booking-session.ts         # Saves booking data
├── booking.ts                      # Lead management
├── contact.ts                      # Contact form submissions
└── newsletter.ts                   # Newsletter signups
```

### Key Features

1. **Multi-step Form Flow**
   - Step 1: Basic Information (name, email, company, website)
   - Step 2: Business Context (type, funding, stage, industry)
   - Step 3: Marketing Details (ads, budget, management, timeline)
   - Step 4: Session Preparation (challenges, outcomes, materials)
   - Step 5: AI Summary Review

2. **Payment Integration**
   - Stripe checkout with automatic Danish VAT calculation
   - Support for both test and production environments
   - Pre-configured product support via STRIPE_MARKETING_THERAPY_PRICE_ID

3. **Scheduling Integration**
   - Cal.com embed on success page
   - Pre-fills customer information from payment
   - Passes payment metadata to Cal.com

4. **Data Persistence**
   - Currently uses localStorage for lead ID tracking
   - Prepared for database integration (Neon/Supabase)

## Tomorrow's Tasks

### 1. Optimize Form UI/UX
- Review and optimize input fields in the booking flow
- Identify which fields are truly necessary
- Improve UI layout and user experience
- Consider progressive disclosure for complex sections

### 2. Implement Firecrawl Integration
- Set up Firecrawl for Step 1 to automatically crawl the provided website
- Extract relevant business information from the homepage
- Handle edge cases:
  - Invalid URLs
  - Sites that block crawling
  - Slow/unresponsive sites
- Integrate crawled data with OpenAI for intelligent form pre-filling

### 3. Implement TipTap Rich Text Editor
- Replace basic textarea in AI summary with TipTap editor
- Set up proper TipTap UI components
- Enable rich text formatting capabilities
- Reference: https://github.com/ueberdosis/tiptap

### 4. Enhance Summary Page
- Make the summary page more user-friendly
- Quick edit functionality for key fields
- Better visual hierarchy
- Inline editing capabilities
- Preview mode vs edit mode

### 5. Complete Stripe Testing
- Full end-to-end test of Stripe checkout flow
- Test webhook handling
- Verify VAT calculations
- Test error scenarios
- Ensure proper receipt generation

### 6. Complete Cal.com Integration Testing
- Test the embed functionality
- Verify data pre-filling
- Test appointment booking flow
- Ensure proper event creation
- Test calendar invitations

### 7. Database Migration Decision
- Evaluate Neon vs Supabase for production database
- Current tables needed:
  - `leads` - Store form submissions and progress
  - `bookings` - Store completed bookings
  - `newsletter_subscriptions` - Newsletter signups
  - `contact_submissions` - Contact form entries
- Consider migration path from current localStorage approach

## Environment Variables

### Required for Production
```env
# Stripe
STRIPE_SECRET_KEY=sk_live_xxx
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_xxx
STRIPE_MARKETING_THERAPY_PRICE_ID=price_xxx

# Cal.com
VITE_CAL_URL=https://cal.com/username/event-slug

# Database
DATABASE_URL=postgresql://xxx

# App
VITE_APP_URL=https://yourdomain.com
```

### Test Environment
```env
# Stripe Test Keys
STRIPE_SECRET_KEY=sk_test_xxx
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_xxx
STRIPE_MARKETING_THERAPY_PRICE_ID=price_test_xxx
```

## Integration Flow

1. User fills multi-step form
2. AI generates comprehensive summary
3. User reviews and edits summary
4. Stripe checkout for payment (with VAT)
5. Payment verification
6. Redirect to success page
7. Cal.com embed for scheduling
8. Confirmation emails sent

## Notes

- All text is in Danish for launch
- Price is 1500 DKK + 25% VAT = 1875 DKK total
- Session duration is 60 minutes
- Form data persists across page refreshes
- Analytics tracking implemented via PostHog