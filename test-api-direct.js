// Quick test to check API directly
const testAPI = async () => {
  console.log('Testing API endpoints...\n');
  
  // Test health endpoint
  try {
    console.log('1. Testing /api/health...');
    const healthResponse = await fetch('http://localhost:3000/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
  
  // Test through Vite proxy
  try {
    console.log('\n2. Testing through Vite proxy at :8082...');
    const proxyResponse = await fetch('http://localhost:8082/api/health');
    const proxyData = await proxyResponse.json();
    console.log('✅ Proxy health check:', proxyData);
  } catch (error) {
    console.error('❌ Proxy health check failed:', error.message);
  }
  
  // Test website analysis
  try {
    console.log('\n3. Testing website analysis...');
    const analysisResponse = await fetch('http://localhost:3000/api/analyze-website-comprehensive', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: 'https://example.com' })
    });
    
    if (!analysisResponse.ok) {
      const errorText = await analysisResponse.text();
      throw new Error(`Status ${analysisResponse.status}: ${errorText}`);
    }
    
    const analysisData = await analysisResponse.json();
    console.log('✅ Analysis test:', analysisData.success ? 'Success' : 'Failed');
  } catch (error) {
    console.error('❌ Analysis test failed:', error.message);
  }
};

testAPI();