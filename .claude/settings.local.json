{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(cp:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(touch:*)", "Bash(grep:*)", "WebFetch(domain:cal.com)", "Bash(git push:*)", "Bash(git pull:*)", "Bash(git merge:*)", "WebFetch(domain:www.asger.me)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "WebFetch(domain:console.cloud.google.com)", "<PERSON><PERSON>(openssl rand:*)", "<PERSON><PERSON>(mv:*)", "Bash(git revert:*)", "Bash(node:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./use-test-env.sh:*)"], "deny": []}}