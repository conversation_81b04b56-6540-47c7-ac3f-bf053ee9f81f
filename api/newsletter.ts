import type { VercelRequest, VercelResponse } from '@vercel/node';
import { neon } from '@neondatabase/serverless';

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { email, name, source = 'website' } = req.body;

    // Validate required fields
    if (!email) {
      return res.status(400).json({ error: 'E-mail er påkrævet' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Ugyldigt e-mail format' });
    }

    // Check if email already exists
    const existingSubscription = await sql`
      SELECT id, status FROM newsletter_subscriptions WHERE email = ${email}
    `;

    if (existingSubscription.length > 0) {
      const subscription = existingSubscription[0];
      
      if (subscription.status === 'active') {
        return res.status(200).json({
          success: true,
          message: 'E-mail er allerede tilmeldt',
          subscriptionId: subscription.id,
          alreadySubscribed: true
        });
      } else {
        // Reactivate subscription
        const result = await sql`
          UPDATE newsletter_subscriptions 
          SET status = 'active', unsubscribed_at = NULL, name = ${name || null}
          WHERE email = ${email}
          RETURNING id, subscribed_at
        `;

        return res.status(200).json({
          success: true,
          message: 'Nyhedsbrev tilmelding genaktiveret',
          subscriptionId: result[0].id,
          reactivated: true
        });
      }
    }

    // Create new subscription
    const result = await sql`
      INSERT INTO newsletter_subscriptions (email, name, source, status)
      VALUES (${email}, ${name || null}, ${source}, 'active')
      RETURNING id, subscribed_at
    `;

    const subscription = result[0];

    // Log successful subscription
    console.log('Newsletter subscription:', {
      id: subscription.id,
      email,
      name,
      source,
      timestamp: subscription.subscribed_at
    });

    // Send Slack notification
    try {
      await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/slack-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'newsletter_signup',
          data: {
            email,
            name,
            source,
            subscriptionId: subscription.id
          }
        }),
      });
    } catch (error) {
      // Don't fail the subscription if Slack notification fails
      console.error('Failed to send Slack notification:', error);
    }

    return res.status(201).json({
      success: true,
      message: 'Succesfuldt tilmeldt nyhedsbrev',
      subscriptionId: subscription.id,
      timestamp: subscription.subscribed_at
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    
    if (error instanceof Error) {
      return res.status(500).json({
        error: 'Kunne ikke tilmelde til nyhedsbrev',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    return res.status(500).json({
      error: 'Intern serverfejl'
    });
  }
}
