import type { VercelRequest, VercelResponse } from '@vercel/node';
import Stripe from 'stripe';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    Object.entries(corsHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { session_id } = req.query;

  if (!session_id || typeof session_id !== 'string') {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil',
    });

    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(session_id);

    // Extract metadata
    const metadata = session.metadata || {};
    const leadData = metadata.lead_data ? JSON.parse(metadata.lead_data) : {};

    // Return session details
    return res.status(200).json({
      success: true,
      payment_status: session.payment_status,
      customer_email: session.customer_email,
      amount_total: session.amount_total ? session.amount_total / 100 : 0, // Convert from øre to DKK
      currency: session.currency,
      booking_details: {
        session_date: leadData.sessionDate,
        customer_name: (leadData.firstName || '') + ' ' + (leadData.lastName || ''),
        company: leadData.companyName,
        session_goals: leadData.sessionGoals,
      },
    });

  } catch (error) {
    console.error('Error verifying payment session:', error);
    
    // Check if it's a Stripe error
    if (error instanceof Stripe.errors.StripeError) {
      return res.status(400).json({
        error: 'Invalid session',
        message: error.message,
      });
    }
    
    return res.status(500).json({
      error: 'Failed to verify payment session',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}