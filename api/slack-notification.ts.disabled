import type { VercelRequest, VercelResponse } from '@vercel/node';
import { neon } from '@neondatabase/serverless';

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

interface SlackMessage {
  text: string;
  blocks?: Array<{
    type: string;
    text?: {
      type: string;
      text: string;
      emoji?: boolean;
    };
    elements?: Array<any>;
    fields?: Array<{
      type: string;
      text: string;
    }>;
  }>;
}

async function sendSlackNotification(message: SlackMessage): Promise<boolean> {
  const webhookUrl = process.env.SLACK_WEBHOOK_URL;
  
  if (!webhookUrl) {
    console.error('SLACK_WEBHOOK_URL not configured');
    return false;
  }

  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      console.error('Slack notification failed:', response.status, await response.text());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error sending Slack notification:', error);
    return false;
  }
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    Object.entries(corsHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { type, data } = req.body;

    let slackMessage: SlackMessage;

    switch (type) {
      case 'booking_completed':
        slackMessage = {
          text: `🎉 New Marketing Therapy Booking!`,
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '🎉 New Marketing Therapy Session Booked!',
                emoji: true
              }
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Name:*\n${data.firstName} ${data.lastName}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Email:*\n${data.email}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Company:*\n${data.companyName || 'Not provided'}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Website:*\n${data.website || 'Not provided'}`
                }
              ]
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*Marketing Challenges:*\n${data.marketingChallenges || 'Not provided'}`
              }
            },
            {
              type: 'context',
              elements: [
                {
                  type: 'mrkdwn',
                  text: `Booking completed at ${new Date().toLocaleString('da-DK', { timeZone: 'Europe/Copenhagen' })}`
                }
              ]
            }
          ]
        };
        break;

      case 'payment_success':
        slackMessage = {
          text: `💰 Payment Received - ${data.amount} DKK`,
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '💰 Payment Successful!',
                emoji: true
              }
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Customer:*\n${data.customerName}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Email:*\n${data.customerEmail}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Amount:*\n${data.amount} DKK`
                },
                {
                  type: 'mrkdwn',
                  text: `*Service:*\n${data.serviceName}`
                }
              ]
            }
          ]
        };
        break;

      case 'contact_form':
        slackMessage = {
          text: `📧 New Contact Form Submission`,
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '📧 New Contact Form Message',
                emoji: true
              }
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Name:*\n${data.name}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Email:*\n${data.email}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Company:*\n${data.company || 'Not provided'}`
                }
              ]
            },
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `*Message:*\n${data.message}`
              }
            }
          ]
        };
        break;

      case 'newsletter_signup':
        slackMessage = {
          text: `📰 New Newsletter Subscriber: ${data.email}`,
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `📰 *New Newsletter Subscriber*\n\n*Email:* ${data.email}\n*Name:* ${data.name || 'Not provided'}\n*Source:* ${data.source || 'website'}`
              }
            }
          ]
        };
        break;

      case 'booking_intent':
        slackMessage = {
          text: `🎯 Booking Form Completed - ${data.name}`,
          blocks: [
            {
              type: 'header',
              text: {
                type: 'plain_text',
                text: '🎯 Booking Form Completed - Awaiting Cal.com Booking',
                emoji: true
              }
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Name:*\n${data.name}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Email:*\n${data.email}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Company:*\n${data.company}`
                },
                {
                  type: 'mrkdwn',
                  text: `*Website:*\n${data.website}`
                }
              ]
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Session ID:*\n${data.sessionId}`
                },
                {
                  type: 'mrkdwn',
                  text: `*AI Summary:*\n${data.hasAISummary ? '✅ Generated' : '❌ Not generated'}`
                }
              ]
            },
            {
              type: 'context',
              elements: [
                {
                  type: 'mrkdwn',
                  text: `⏳ *Status:* Redirected to Cal.com for time selection and payment`
                }
              ]
            }
          ]
        };
        break;

      default:
        return res.status(400).json({ error: 'Invalid notification type' });
    }

    // Send to Slack
    const sent = await sendSlackNotification(slackMessage);

    // Log to database
    await sql`
      INSERT INTO notifications_log (type, payload, sent_to_slack)
      VALUES (${type}, ${JSON.stringify(data)}, ${sent})
    `;

    if (!sent) {
      return res.status(500).json({ 
        error: 'Failed to send Slack notification',
        logged: true 
      });
    }

    return res.status(200).json({ 
      success: true,
      message: 'Notification sent successfully' 
    });

  } catch (error) {
    console.error('Notification error:', error);
    
    return res.status(500).json({
      error: 'Failed to process notification',
      details: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
}