import type { VercelRequest, VercelResponse } from '@vercel/node';
import { format, addDays, startOfWeek, endOfWeek, isToday, isTomorrow, startOfDay, setHours, setMinutes } from 'date-fns';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

// Time slots configuration
const WEEKDAY_TIME_SLOTS = ['09:00', '10:00', '11:00', '12:00'];
const WEEKEND_TIME_SLOTS = ['10:00', '12:00', '14:00'];
const SESSION_DURATION_MINUTES = 120;

interface AvailabilitySummary {
  thisWeek: number;
  nextWeek: number;
  todayBookings: number;
  nextAvailable: string | null;
  totalAvailableThisMonth: number;
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    Object.entries(corsHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const now = new Date();
    const today = startOfDay(now);
    
    // Calculate date ranges
    const thisWeekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday
    const thisWeekEnd = endOfWeek(now, { weekStartsOn: 1 }); // Sunday
    const nextWeekStart = addDays(thisWeekEnd, 1);
    const nextWeekEnd = addDays(nextWeekStart, 6);
    
    let thisWeekCount = 0;
    let nextWeekCount = 0;
    let todayBookingsCount = 0;
    let nextAvailableSlot: { date: Date, time: string } | null = null;
    let totalThisMonth = 0;

    // Check if Google Calendar is configured
    const hasGoogleCalendar = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                             process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;

    if (!hasGoogleCalendar) {
      // Return realistic mock data when calendar is not configured
      
      // Simulate availability for this week (fewer slots as week progresses)
      const daysLeftThisWeek = Math.max(0, 7 - now.getDay());
      thisWeekCount = Math.max(1, Math.floor(Math.random() * 3) + (daysLeftThisWeek > 3 ? 2 : 0));
      
      // Next week has more availability
      nextWeekCount = Math.floor(Math.random() * 5) + 3;
      
      // Simulate today's bookings (0-2)
      todayBookingsCount = Math.floor(Math.random() * 3);
      
      // Calculate next available slot
      const possibleTimes = ['om 2 timer', 'i morgen kl. 10:00', 'tirsdag kl. 14:00', 'onsdag kl. 11:00'];
      const nextAvailableTime = possibleTimes[Math.floor(Math.random() * possibleTimes.length)];
      
      // Total for the month
      totalThisMonth = thisWeekCount + nextWeekCount + Math.floor(Math.random() * 10) + 5;

      return res.status(200).json({
        thisWeek: thisWeekCount,
        nextWeek: nextWeekCount,
        todayBookings: todayBookingsCount,
        nextAvailable: nextAvailableTime,
        totalAvailableThisMonth: totalThisMonth,
        mock: true
      });
    }

    // Try to use Google Calendar API if available
    try {
      const { google } = await import('googleapis');
      
      // Initialize Google Calendar API
      const auth = new google.auth.GoogleAuth({
        credentials: {
          client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
          private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY.replace(/\\n/g, '\n'),
        },
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
      });

      const calendar = google.calendar({ version: 'v3', auth });
      const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';

      // Fetch events for the next 30 days
      const endDate = addDays(now, 30);
      
      const response = await calendar.events.list({
        calendarId,
        timeMin: now.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
        timeZone: 'Europe/Copenhagen',
      });

      const events = response.data.items || [];
      
      // Count bookings for today
      todayBookingsCount = events.filter(event => {
        if (!event.start?.dateTime) return false;
        const eventDate = new Date(event.start.dateTime);
        return isToday(eventDate) && event.summary?.toLowerCase().includes('marketing');
      }).length;

      // Check availability for each day in the next 30 days
      for (let i = 0; i < 30; i++) {
        const checkDate = addDays(today, i);
        const dayOfWeek = checkDate.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        const timeSlots = isWeekend ? WEEKEND_TIME_SLOTS : WEEKDAY_TIME_SLOTS;

        for (const timeSlot of timeSlots) {
          const [hours, minutes] = timeSlot.split(':').map(Number);
          const slotStart = setMinutes(setHours(checkDate, hours), minutes);
          const slotEnd = new Date(slotStart.getTime() + SESSION_DURATION_MINUTES * 60 * 1000);

          // Skip if in the past or too soon
          if (slotStart < new Date(Date.now() + 18 * 60 * 60 * 1000)) continue;

          // Check if slot is available (no conflicting events)
          const hasConflict = events.some(event => {
            if (!event.start?.dateTime || !event.end?.dateTime) return false;
            const eventStart = new Date(event.start.dateTime);
            const eventEnd = new Date(event.end.dateTime);
            return slotStart < eventEnd && slotEnd > eventStart;
          });

          if (!hasConflict) {
            // Count this available slot
            if (checkDate >= thisWeekStart && checkDate <= thisWeekEnd) {
              thisWeekCount++;
            } else if (checkDate >= nextWeekStart && checkDate <= nextWeekEnd) {
              nextWeekCount++;
            }
            
            if (i < 30) {
              totalThisMonth++;
            }

            // Track the next available slot
            if (!nextAvailableSlot) {
              nextAvailableSlot = { date: checkDate, time: timeSlot };
            }
          }
        }
      }

      // Format next available slot
      let nextAvailableFormatted = null;
      if (nextAvailableSlot) {
        if (isToday(nextAvailableSlot.date)) {
          nextAvailableFormatted = `i dag kl. ${nextAvailableSlot.time}`;
        } else if (isTomorrow(nextAvailableSlot.date)) {
          nextAvailableFormatted = `i morgen kl. ${nextAvailableSlot.time}`;
        } else {
          const dayNames = ['søndag', 'mandag', 'tirsdag', 'onsdag', 'torsdag', 'fredag', 'lørdag'];
          const dayName = dayNames[nextAvailableSlot.date.getDay()];
          nextAvailableFormatted = `${dayName} kl. ${nextAvailableSlot.time}`;
        }
      }

      return res.status(200).json({
        thisWeek: thisWeekCount,
        nextWeek: nextWeekCount,
        todayBookings: todayBookingsCount,
        nextAvailable: nextAvailableFormatted,
        totalAvailableThisMonth: totalThisMonth,
        mock: false
      });

    } catch (error) {
      console.error('Google Calendar error:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error getting availability summary:', error);
    
    // Return fallback mock data on error
    return res.status(200).json({
      thisWeek: 2,
      nextWeek: 5,
      todayBookings: 1,
      nextAvailable: 'i morgen kl. 10:00',
      totalAvailableThisMonth: 15,
      mock: true,
      error: 'Using mock data due to configuration issue'
    });
  }
}