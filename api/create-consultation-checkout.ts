import type { VercelRequest, VercelResponse } from '@vercel/node';
import Stripe from 'stripe';

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Metode ikke tilladt' });
  }

  try {
    const { formData, serviceId, priceInDKK, successUrl, cancelUrl } = req.body;

    // Validate input
    if (!formData || !serviceId || !priceInDKK) {
      return res.status(400).json({ error: 'Manglende påkrævede felter' });
    }

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil',
    });

    // Check if we have a pre-configured price ID
    const priceId = process.env.STRIPE_MARKETING_THERAPY_PRICE_ID;
    
    // Create line items based on whether we have a price ID
    const lineItems = priceId && priceId !== 'price_xxxxxxxxxxxxxx' ? 
      // Use pre-configured product with proper VAT settings
      [{
        price: priceId,
        quantity: 1,
      }] : 
      // Fallback to dynamic pricing
      [{
        price_data: {
          currency: 'dkk',
          product_data: {
            name: 'Marketing Terapi Session - 60 minutter',
            description: 'Personlig marketing konsultation med Asger Teglgaard (moms tillægges automatisk)',
            metadata: {
              service_id: serviceId,
              customer_email: formData.email,
              customer_name: `${formData.firstName} ${formData.lastName}`,
              company_name: formData.companyName
            }
          },
          unit_amount: priceInDKK * 100, // Convert to øre
        },
        quantity: 1,
      }];

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: formData.email,
      metadata: {
        service_id: serviceId,
        lead_data: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          companyName: formData.companyName,
          website: formData.website,
          aiSummary: formData.aiSummary,
          sessionDate: formData.sessionDate,
          sessionGoals: formData.sessionGoals,
          specificFocus: formData.specificFocus,
          marketingChallenges: formData.primaryChallenge,
          marketingBudget: formData.marketingBudget,
          marketingSetup: formData.marketingSetup,
          isWeekend: priceInDKK === 6000 // Weekend sessions are 6000 DKK
        })
      },
      // Danish VAT
      automatic_tax: {
        enabled: true,
      },
      tax_id_collection: {
        enabled: true,
      },
      locale: 'da',
    });

    // Send Slack notification about checkout initiation
    try {
      await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/slack-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'booking_completed',
          data: {
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            companyName: formData.companyName,
            website: formData.website,
            marketingChallenges: formData.marketingChallenges,
            sessionId: session.id
          }
        }),
      });
    } catch (error) {
      // Don't fail the checkout if Slack notification fails
      console.error('Failed to send Slack notification:', error);
    }

    return res.status(200).json({ 
      checkoutUrl: session.url,
      sessionId: session.id
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return res.status(500).json({ 
      error: 'Kunne ikke oprette betalingssession',
      details: error instanceof Error ? error.message : 'Ukendt fejl'
    });
  }
}