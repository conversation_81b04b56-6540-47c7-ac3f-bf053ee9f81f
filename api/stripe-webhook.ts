import type { VercelRequest, VercelResponse } from '@vercel/node';
import Stripe from 'stripe';
// import { neon } from '@neondatabase/serverless';
import { buffer } from 'micro';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

// Initialize Neon client - commented out as database is optional
// const sql = neon(process.env.DATABASE_URL!);

// Stripe webhook handler
export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sig = req.headers['stripe-signature'] as string;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

  let event: Stripe.Event;

  try {
    // Verify webhook signature and extract the event
    // For Vercel, we need to read the raw body
    const buf = await buffer(req);
    const rawBody = buf.toString('utf8');
    
    event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return res.status(400).json({ error: 'Webhook signature verification failed' });
  }

  // Handle the event
  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Extract metadata
        const leadData = session.metadata?.lead_data ? JSON.parse(session.metadata.lead_data) : {};
        
        // Update lead as converted customer - commented out as database is optional
        // if (leadData.email || session.customer_email) {
        //   await sql`
        //     UPDATE leads
        //     SET converted_to_customer = true, is_completed = true
        //     WHERE email = ${leadData.email || session.customer_email}
        //   `;
        // }

        // Create calendar event for the booking
        if (leadData.sessionDate) {
          try {
            const calendarResponse = await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/create-calendar-event`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.INTERNAL_API_KEY}`,
              },
              body: JSON.stringify({
                customerName: `${leadData.firstName} ${leadData.lastName}`,
                customerEmail: leadData.email || session.customer_email,
                sessionDateTime: leadData.sessionDate,
                sessionGoals: leadData.sessionGoals || '',
                specificFocus: leadData.specificFocus,
                companyName: leadData.companyName,
                website: leadData.website,
                marketingChallenges: leadData.marketingChallenges,
              }),
            });

            if (!calendarResponse.ok) {
              console.error('Failed to create calendar event:', await calendarResponse.text());
            } else {
              const calendarData = await calendarResponse.json();
              console.log('Calendar event created:', calendarData);
              
              // Send booking confirmation email
              try {
                const emailResponse = await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/send-booking-email`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.INTERNAL_API_KEY}`,
                  },
                  body: JSON.stringify({
                    customerName: `${leadData.firstName} ${leadData.lastName}`,
                    customerEmail: leadData.email || session.customer_email,
                    sessionDateTime: leadData.sessionDate,
                    sessionGoals: leadData.sessionGoals || '',
                    specificFocus: leadData.specificFocus,
                    companyName: leadData.companyName,
                    website: leadData.website,
                    priceInDKK: leadData.isWeekend ? 6000 : 3000,
                    hangoutLink: calendarData.hangoutLink,
                  }),
                });
                
                if (!emailResponse.ok) {
                  console.error('Failed to send booking email:', await emailResponse.text());
                } else {
                  console.log('Booking confirmation email sent');
                }
              } catch (emailError) {
                console.error('Error sending booking email:', emailError);
              }
            }
          } catch (error) {
            console.error('Error creating calendar event:', error);
          }
        }

        // Send Slack notification for successful payment
        try {
          await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/slack-notification`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'payment_success',
              data: {
                customerName: `${leadData.firstName} ${leadData.lastName}`,
                customerEmail: leadData.email || session.customer_email,
                amount: session.amount_total ? session.amount_total / 100 : 0, // Convert from øre to DKK
                serviceName: 'Marketing Terapi Session',
                sessionId: session.id,
                paymentIntent: session.payment_intent,
                sessionDate: leadData.sessionDate,
              }
            }),
          });
        } catch (error) {
          console.error('Failed to send Slack notification:', error);
        }

        // Log successful payment
        console.log('Payment successful:', {
          sessionId: session.id,
          customerEmail: session.customer_email,
          amount: session.amount_total,
          sessionDate: leadData.sessionDate,
        });
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        
        // Log failed payment
        console.error('Payment failed:', {
          paymentIntentId: paymentIntent.id,
          error: paymentIntent.last_payment_error?.message,
        });

        // Could send notification about failed payment
        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted': {
        // Handle subscription events if you add subscription services later
        const subscription = event.data.object as Stripe.Subscription;
        console.log(`Subscription ${event.type}:`, subscription.id);
        break;
      }

      default:
        // Log unhandled event types
        console.log(`Unhandled event type: ${event.type}`);
    }

    // Return a response to acknowledge receipt of the event
    return res.status(200).json({ received: true });

  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({ error: 'Webhook processing failed' });
  }
}

// Disable body parsing for Stripe webhooks (they need raw body)
export const config = {
  api: {
    bodyParser: false,
  },
};