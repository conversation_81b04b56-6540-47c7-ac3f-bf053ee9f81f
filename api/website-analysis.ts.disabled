import { VercelRequest, VercelResponse } from '@vercel/node';
import FirecrawlApp from '@mendable/firecrawl-js';
import OpenAI from 'openai';

// Initialize only if API keys are available
const firecrawl = process.env.FIRECRAWL_API_KEY 
  ? new FirecrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY })
  : null;

const openai = process.env.OPENAI_API_KEY
  ? new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
  : null;

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { url, mode = 'metadata' } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Check if services are available
    if (!firecrawl) {
      console.error('Missing Firecrawl API key');
      return res.status(500).json({ 
        error: 'Service temporarily unavailable',
        details: 'Missing configuration'
      });
    }

    // Normalize URL
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http')) {
      normalizedUrl = `https://${normalizedUrl}`;
    }

    console.log(`${mode === 'full' ? 'Analyzing' : 'Fetching metadata for'} website:`, normalizedUrl);

    // Use Firecrawl to scrape the website
    const scrapeResult = await firecrawl.scrapeUrl(normalizedUrl, {
      onlyMainContent: mode === 'full',
      waitFor: mode === 'full' ? 2000 : 1000
    });

    if (!scrapeResult.success) {
      console.error('Firecrawl scrape failed:', scrapeResult);
      throw new Error(`Failed to ${mode === 'full' ? 'analyze' : 'fetch metadata for'} website`);
    }

    const metadata = scrapeResult.metadata || {};

    // If metadata only mode, return just the metadata
    if (mode === 'metadata') {
      return res.status(200).json({
        success: true,
        metadata: {
          title: metadata.title || '',
          description: metadata.description || '',
          image: metadata.ogImage || '',
          siteName: metadata.siteName || new URL(normalizedUrl).hostname.replace('www.', ''),
          favicon: `https://www.google.com/s2/favicons?domain=${normalizedUrl}&sz=64`
        }
      });
    }

    // Full analysis mode
    if (!openai) {
      console.error('Missing OpenAI API key');
      return res.status(500).json({ 
        error: 'Analysis service temporarily unavailable',
        details: 'Missing configuration'
      });
    }

    const websiteContent = scrapeResult.markdown || '';

    // Use OpenAI to analyze the content
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `You are a website analyzer. Extract the following information from the website content:
          1. Company name (be precise, use the exact name found)
          2. Industry/sector
          3. Main products or services
          4. Target audience
          
          Return the data in JSON format with these exact keys:
          - companyName: string
          - industry: string (in Danish)
          - products: string (brief description in Danish)
          - targetAudience: string (brief description in Danish)
          
          If you cannot find certain information, leave it as an empty string.`
        },
        {
          role: "user",
          content: `Website URL: ${normalizedUrl}
          
          Website metadata:
          Title: ${metadata.title || 'N/A'}
          Description: ${metadata.description || 'N/A'}
          
          Website content (first 3000 chars):
          ${websiteContent.substring(0, 3000)}`
        }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" }
    });

    const analysis = JSON.parse(completion.choices[0].message.content || '{}');

    return res.status(200).json({
      success: true,
      ...analysis,
      metadata: {
        title: metadata.title,
        description: metadata.description,
        ogImage: metadata.ogImage
      }
    });

  } catch (error) {
    console.error(`Error ${req.body.mode === 'full' ? 'analyzing' : 'fetching metadata for'} website:`, error);
    
    // Return a graceful error response
    return res.status(500).json({ 
      error: `Failed to ${req.body.mode === 'full' ? 'analyze' : 'fetch metadata for'} website`,
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}