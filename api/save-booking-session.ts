import type { VercelRequest, VercelResponse } from '@vercel/node';
import { neon } from '@neondatabase/serverless';

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Metode ikke tilladt' });
  }

  try {
    const { formData, leadId } = req.body;

    // Create a session ID
    const sessionId = 'booking_' + Math.random().toString(36).substr(2, 9);

    // Update lead with booking session data
    if (leadId) {
      await sql`
        UPDATE leads
        SET 
          is_completed = true,
          booking_session_id = ${sessionId},
          ai_summary = ${formData.aiSummary || null},
          updated_at = NOW()
        WHERE id = ${leadId}
      `;
    }

    // Send Slack notification for booking intent
    try {
      await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/slack-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'booking_intent',
          data: {
            name: `${formData.firstName} ${formData.lastName}`,
            email: formData.email,
            company: formData.companyName,
            website: formData.website,
            sessionId: sessionId,
            hasAISummary: !!formData.aiSummary
          }
        }),
      });
    } catch (error) {
      console.error('Failed to send Slack notification:', error);
    }

    // Create Cal.com booking URL with metadata
    const calUrl = process.env.VITE_CAL_URL || 'https://cal.com/asger.me/marketing-terapi-session';
    
    // Build query parameters for Cal.com
    const params = new URLSearchParams({
      name: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      metadata: JSON.stringify({
        sessionId: sessionId,
        leadId: leadId,
        company: formData.companyName,
        website: formData.website,
        source: 'booking-form'
      })
    });

    const bookingUrl = `${calUrl}?${params.toString()}`;

    return res.status(200).json({
      success: true,
      sessionId,
      bookingUrl
    });

  } catch (error) {
    console.error('Error saving booking session:', error);
    
    return res.status(500).json({
      error: 'Kunne ikke gemme booking session',
      details: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
}