import type { VercelRequest, VercelResponse } from '@vercel/node';
import { neon } from '@neondatabase/serverless';

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, email, company, message } = req.body;

    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({ 
        error: 'Manglende påkrævede felter: navn, e-mail og besked er påkrævet' 
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Ugyldigt e-mail format' });
    }

    // Insert contact submission into database
    const result = await sql`
      INSERT INTO contact_submissions (name, email, company, message, source, status)
      VALUES (${name}, ${email}, ${company || null}, ${message}, 'contact_form', 'new')
      RETURNING id, created_at
    `;

    const submission = result[0];

    // Log successful submission
    console.log('Contact form submission:', {
      id: submission.id,
      email,
      name,
      company,
      timestamp: submission.created_at
    });

    // Send Slack notification
    try {
      await fetch(`${process.env.VITE_APP_URL || 'https://asger.me'}/api/slack-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'contact_form',
          data: {
            name,
            email,
            company,
            message,
            submissionId: submission.id
          }
        }),
      });
    } catch (error) {
      // Don't fail the submission if Slack notification fails
      console.error('Failed to send Slack notification:', error);
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Kontaktformular indsendt succesfuldt',
      submissionId: submission.id,
      timestamp: submission.created_at
    });

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    // Handle database errors
    if (error instanceof Error) {
      return res.status(500).json({
        error: 'Kunne ikke indsende kontaktformular',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    return res.status(500).json({
      error: 'Intern serverfejl'
    });
  }
}
