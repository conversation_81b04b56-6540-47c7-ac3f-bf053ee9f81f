import type { VercelRequest, VercelResponse } from '@vercel/node';
import { format, addDays, setHours, setMinutes, startOfDay, endOfDay } from 'date-fns';

// Time slots configuration (in 24-hour format)
// For intro calls (10-30 minutes), we can have more flexible hours
const WEEKDAY_TIME_SLOTS = [
  '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'
];

const WEEKEND_TIME_SLOTS = [
  '10:00', '12:00', '14:00'
];

// Timezone for business hours (Copenhagen/Denmark)
const TIMEZONE = 'Europe/Copenhagen';

// Business hours configuration
const BUSINESS_DAYS = [1, 2, 3, 4, 5]; // Monday = 1, Friday = 5 (JavaScript getDay())
const WEEKEND_DAYS = [0, 6]; // Sunday = 0, Saturday = 6
const BUSINESS_START_HOUR = 9;
const BUSINESS_END_HOUR = 17;
const BUSINESS_END_MINUTE = 0;

const SESSION_DURATION_MINUTES = 30; // 30 minutes for intro calls

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    Object.entries(corsHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { date } = req.query;

  if (!date || typeof date !== 'string') {
    return res.status(400).json({ error: 'Date parameter is required' });
  }

  try {
    // Dynamic import for timezone handling
    let zonedTimeToUtc: any;
    let utcToZonedTime: any;
    
    try {
      const dateFnsTz = await import('date-fns-tz');
      zonedTimeToUtc = dateFnsTz.fromZonedTime;
      utcToZonedTime = dateFnsTz.toZonedTime;
    } catch (error) {
      console.error('Failed to import date-fns-tz, using fallback:', error);
      // Fallback: adjust for Copenhagen timezone offset (UTC+1 or UTC+2 for DST)
      zonedTimeToUtc = (date: Date, tz: string) => {
        // Simple offset for Copenhagen (this is a rough approximation)
        const offset = 2; // Summer time (UTC+2)
        return new Date(date.getTime() - offset * 60 * 60 * 1000);
      };
      utcToZonedTime = (date: Date, tz: string) => {
        const offset = 2; // Summer time (UTC+2)
        return new Date(date.getTime() + offset * 60 * 60 * 1000);
      };
    }
    const requestedDate = new Date(date);
    
    // Don't allow checking dates in the past
    if (requestedDate < startOfDay(new Date())) {
      return res.status(400).json({ error: 'Cannot check availability for past dates' });
    }

    // Don't allow checking dates more than 60 days in the future
    if (requestedDate > addDays(new Date(), 60)) {
      return res.status(400).json({ error: 'Cannot check availability more than 60 days in advance' });
    }

    // Check if the requested date is a weekday or weekend
    const dayOfWeek = requestedDate.getDay();
    const isWeekend = WEEKEND_DAYS.includes(dayOfWeek);
    const TIME_SLOTS = isWeekend ? WEEKEND_TIME_SLOTS : WEEKDAY_TIME_SLOTS;

    // Check if Google Calendar is configured
    if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY) {
      console.log('Google Calendar not configured, returning mock data');
      const mockSlots = TIME_SLOTS.map(timeSlot => {
        const [hours, minutes] = timeSlot.split(':').map(Number);
        const slotStart = setMinutes(setHours(requestedDate, hours), minutes);
        const slotEnd = new Date(slotStart.getTime() + SESSION_DURATION_MINUTES * 60 * 1000);
        const isPast = slotStart < new Date();
        const tooSoon = slotStart < new Date(Date.now() + 18 * 60 * 60 * 1000);
        
        // Check if the slot end time exceeds business hours (14:30)
        const businessEndTime = setMinutes(setHours(requestedDate, BUSINESS_END_HOUR), BUSINESS_END_MINUTE);
        const exceedsBusinessHours = slotEnd > businessEndTime;
        
        return {
          date: requestedDate,
          time: timeSlot,
          available: !isPast && !tooSoon && !exceedsBusinessHours && Math.random() > 0.3, // 70% availability for valid slots
          isWeekend,
        };
      });
      
      return res.status(200).json({
        date: format(requestedDate, 'yyyy-MM-dd'),
        slots: mockSlots,
        mock: true,
      });
    }

    // Try to use Google Calendar API if available
    try {
      const { google } = await import('googleapis');
      
      // Initialize Google Calendar API
      const auth = new google.auth.GoogleAuth({
        credentials: {
          client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
          private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY.replace(/\\n/g, '\n'),
        },
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
      });

      const calendar = google.calendar({ version: 'v3', auth });

      // Get calendar ID from environment variable
      const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';

      // Fetch events for the requested date
      // Convert to Copenhagen timezone for proper day boundaries
      const startTime = zonedTimeToUtc(startOfDay(requestedDate), TIMEZONE);
      const endTime = zonedTimeToUtc(endOfDay(requestedDate), TIMEZONE);

      console.log('Fetching events for date:', format(requestedDate, 'yyyy-MM-dd'));
      console.log('Time range:', startTime.toISOString(), 'to', endTime.toISOString());

      const response = await calendar.events.list({
        calendarId,
        timeMin: startTime.toISOString(),
        timeMax: endTime.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
        timeZone: TIMEZONE,
      });

      const events = response.data.items || [];
      
      console.log(`Found ${events.length} events on ${format(requestedDate, 'yyyy-MM-dd')}:`);
      events.forEach(event => {
        if (event.start?.dateTime && event.end?.dateTime) {
          const eventStart = utcToZonedTime(new Date(event.start.dateTime), TIMEZONE);
          const eventEnd = utcToZonedTime(new Date(event.end.dateTime), TIMEZONE);
          console.log(`- ${event.summary}: ${format(eventStart, 'HH:mm')} - ${format(eventEnd, 'HH:mm')}`);
        }
      });

      // Check availability for each time slot
      const availableSlots = TIME_SLOTS.map(timeSlot => {
        const [hours, minutes] = timeSlot.split(':').map(Number);
        const slotStart = setMinutes(setHours(requestedDate, hours), minutes);
        const slotEnd = new Date(slotStart.getTime() + SESSION_DURATION_MINUTES * 60 * 1000);

        // Convert slot times to UTC for comparison with calendar events
        const slotStartUtc = zonedTimeToUtc(slotStart, TIMEZONE);
        const slotEndUtc = zonedTimeToUtc(slotEnd, TIMEZONE);

        // Check if the slot end time exceeds business hours (14:30)
        const businessEndTime = setMinutes(setHours(requestedDate, BUSINESS_END_HOUR), BUSINESS_END_MINUTE);
        if (slotEnd > businessEndTime) {
          console.log(`Slot ${timeSlot} exceeds business hours (ends at ${format(slotEnd, 'HH:mm')})`);
          return {
            date: requestedDate,
            time: timeSlot,
            available: false,
            isWeekend,
          };
        }

        // Check if this slot conflicts with any existing events
        const isAvailable = !events.some(event => {
          if (!event.start?.dateTime || !event.end?.dateTime) return false;
          
          const eventStart = new Date(event.start.dateTime);
          const eventEnd = new Date(event.end.dateTime);
          
          // Check for overlap
          const hasOverlap = (slotStartUtc < eventEnd && slotEndUtc > eventStart);
          
          if (hasOverlap) {
            console.log(`Slot ${timeSlot} conflicts with event from ${eventStart.toISOString()} to ${eventEnd.toISOString()}`);
          }
          
          return hasOverlap;
        });

        // Check if the slot is in the past or less than 18 hours away
        const nowUtc = new Date();
        const eighteenHoursFromNow = new Date(nowUtc.getTime() + 18 * 60 * 60 * 1000);
        const isPast = slotStartUtc < nowUtc;
        const tooSoon = slotStartUtc < eighteenHoursFromNow;

        if (isPast) {
          console.log(`Slot ${timeSlot} is in the past`);
        } else if (tooSoon) {
          console.log(`Slot ${timeSlot} is less than 18 hours away`);
        }

        return {
          date: requestedDate,
          time: timeSlot,
          available: isAvailable && !isPast && !tooSoon,
          isWeekend,
        };
      });

      return res.status(200).json({
        date: format(requestedDate, 'yyyy-MM-dd'),
        slots: availableSlots,
      });
    } catch (googleError) {
      console.error('Google Calendar error:', googleError);
      throw googleError;
    }

  } catch (error) {
    console.error('Error checking calendar availability:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      code: (error as any)?.code,
      stack: error instanceof Error ? error.stack : undefined,
      env: {
        hasEmail: !!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        hasKey: !!process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY,
        hasCalendarId: !!process.env.GOOGLE_CALENDAR_ID,
      }
    });
    
    // Return mock data on error
    const requestedDate = new Date(date);
    const dayOfWeek = requestedDate.getDay();
    const isWeekend = WEEKEND_DAYS.includes(dayOfWeek);
    const TIME_SLOTS = isWeekend ? WEEKEND_TIME_SLOTS : WEEKDAY_TIME_SLOTS;
    const mockSlots = TIME_SLOTS.map(timeSlot => {
      const [hours, minutes] = timeSlot.split(':').map(Number);
      const slotStart = setMinutes(setHours(requestedDate, hours), minutes);
      const slotEnd = new Date(slotStart.getTime() + SESSION_DURATION_MINUTES * 60 * 1000);
      const isPast = slotStart < new Date();
      const tooSoon = slotStart < new Date(Date.now() + 18 * 60 * 60 * 1000);
      
      // Check if the slot end time exceeds business hours (14:30)
      const businessEndTime = setMinutes(setHours(requestedDate, BUSINESS_END_HOUR), BUSINESS_END_MINUTE);
      const exceedsBusinessHours = slotEnd > businessEndTime;
      
      return {
        date: requestedDate,
        time: timeSlot,
        available: !isPast && !tooSoon && !exceedsBusinessHours && Math.random() > 0.3, // 70% availability for valid slots
        isWeekend,
      };
    });
    
    return res.status(200).json({
      date: format(requestedDate, 'yyyy-MM-dd'),
      slots: mockSlots,
      mock: true,
      error: 'Using mock data due to calendar configuration issue',
      debug: process.env.NODE_ENV === 'development' ? {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        hasGoogleCreds: !!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && !!process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY
      } : undefined
    });
  }
}