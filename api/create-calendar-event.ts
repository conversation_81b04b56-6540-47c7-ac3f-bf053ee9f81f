import type { VercelRequest, VercelResponse } from '@vercel/node';
import { format, parseISO, addHours } from 'date-fns';
import { da } from 'date-fns/locale';

interface CreateEventData {
  customerName: string;
  customerEmail: string;
  sessionDateTime: string; // Format: 'yyyy-MM-dd HH:mm'
  sessionGoals: string;
  specificFocus?: string;
  companyName?: string;
  website?: string;
  marketingChallenges?: string;
}

export default async function handler(
  req: VercelRequest,
  res: VercelResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is coming from our frontend
  const authHeader = req.headers.authorization;
  const expectedKey = process.env.INTERNAL_API_KEY || process.env.VITE_INTERNAL_API_KEY;
  if (!authHeader || authHeader !== `Bearer ${expectedKey}`) {
    console.error('Unauthorized calendar event request');
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const eventData: CreateEventData = req.body;

    // Validate required fields
    if (!eventData.customerName || !eventData.customerEmail || !eventData.sessionDateTime) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Parse the session date and time
    const [dateStr, timeStr] = eventData.sessionDateTime.split(' ');
    const [year, month, day] = dateStr.split('-').map(Number);
    const [hours, minutes] = timeStr.split(':').map(Number);
    
    const startTime = new Date(year, month - 1, day, hours, minutes);
    const endTime = addHours(startTime, 2); // 2-hour session

    // Import Google APIs dynamically
    const { google } = await import('googleapis');
    
    // Initialize Google Calendar API
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/calendar'],
    });

    const calendar = google.calendar({ version: 'v3', auth });
    const calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';

    // Create event description
    const description = `
Marketing Terapi Session - ${eventData.customerName}

🏢 Virksomhed: ${eventData.companyName || 'Ikke oplyst'}
🌐 Website: ${eventData.website || 'Ikke oplyst'}

📋 SESSION MÅL:
${eventData.sessionGoals}

${eventData.specificFocus ? `📌 SPECIFIKT FOKUS:\n${eventData.specificFocus}\n` : ''}
${eventData.marketingChallenges ? `🎯 MARKETING UDFORDRINGER:\n${eventData.marketingChallenges}\n` : ''}

📝 FORBEREDELSE FØR SESSION:
For at få mest muligt ud af vores tid sammen:

1. ✅ Giv adgang til marketing værktøjer (Google Ads, Facebook, Analytics)
   - Send invitationer til: <EMAIL>
   - Giv "View" eller "Read-only" adgang

2. ✅ Forbered spørgsmål om specifikke udfordringer

3. ✅ Hav data klar - performance tal, budgetter, aktuelle kampagner

4. ✅ Test dit udstyr - internet, kamera og mikrofon

⚠️ VIGTIGE VILKÅR:
• Aflysning skal ske senest 24 timer før
• Session kan ombookes én gang uden gebyr
• No-show resulterer i tabt session

💰 BETALING:
Denne session er bekræftet og betalt via Stripe.

📧 KONTAKT:
Har du spørgsmål? <NAME_EMAIL>

Vi ses snart!
Asger Teglgaard
`.trim();

    // Create the calendar event
    const event = {
      summary: `Marketing Terapi - ${eventData.customerName}`,
      description,
      start: {
        dateTime: startTime.toISOString(),
        timeZone: 'Europe/Copenhagen',
      },
      end: {
        dateTime: endTime.toISOString(),
        timeZone: 'Europe/Copenhagen',
      },
      attendees: [
        {
          email: eventData.customerEmail,
          displayName: eventData.customerName,
        },
      ],
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 24 * 60 }, // 1 day before
          { method: 'email', minutes: 60 }, // 1 hour before
        ],
      },
      conferenceData: {
        createRequest: {
          requestId: `mt-${Date.now()}`,
          conferenceSolutionKey: {
            type: 'hangoutsMeet',
          },
        },
      },
    };

    const response = await calendar.events.insert({
      calendarId,
      requestBody: event,
      conferenceDataVersion: 1,
      sendUpdates: 'all', // Send invitation to attendees
    });

    console.log('Calendar event created:', response.data.id);

    return res.status(200).json({
      success: true,
      eventId: response.data.id,
      htmlLink: response.data.htmlLink,
      hangoutLink: response.data.hangoutLink,
    });

  } catch (error) {
    console.error('Error creating calendar event:', error);
    return res.status(500).json({
      error: 'Failed to create calendar event',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}