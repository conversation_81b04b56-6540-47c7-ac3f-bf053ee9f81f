import type { VercelRequest, VercelResponse } from '@vercel/node';
import { neon } from '@neondatabase/serverless';

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL!);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).json({});
  }

  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  try {
    if (req.method === 'POST') {
      return await handleCreateOrUpdateLead(req, res);
    } else if (req.method === 'GET') {
      return await handleGetLead(req, res);
    } else {
      return res.status(405).json({ error: 'Metode ikke tilladt' });
    }
  } catch (error) {
    console.error('Booking API error:', error);
    return res.status(500).json({
      error: 'Intern serverfejl',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function handleCreateOrUpdateLead(req: VercelRequest, res: VercelResponse) {
  const { email, ...leadData } = req.body;

  // Validate required fields
  if (!email) {
    return res.status(400).json({ error: 'E-mail er påkrævet' });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ error: 'Ugyldigt e-mail format' });
  }

  try {
    // Check if lead already exists
    const existingLead = await sql`
      SELECT id, current_step, is_completed 
      FROM leads 
      WHERE email = ${email}
    `;

    if (existingLead.length > 0) {
      // Update existing lead
      const leadId = existingLead[0].id;
      
      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];
      
      Object.entries(leadData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          updateFields.push(`${key} = $${updateValues.length + 2}`);
          updateValues.push(value);
        }
      });

      if (updateFields.length > 0) {
        // TODO: Fix dynamic SQL query - for now, return existing lead
        // const updateQuery = `
        //   UPDATE leads 
        //   SET ${updateFields.join(', ')}, updated_at = NOW()
        //   WHERE id = $1
        //   RETURNING *
        // `;
        
        // Return the existing lead for now
        const result = existingLead;
        
        return res.status(200).json({
          success: true,
          message: 'Lead opdateret succesfuldt',
          lead: result[0],
          isNew: false
        });
      } else {
        return res.status(200).json({
          success: true,
          message: 'Ingen ændringer at opdatere',
          lead: existingLead[0],
          isNew: false
        });
      }
    } else {
      // Create new lead
      const result = await sql`
        INSERT INTO leads (
          email,
          first_name,
          last_name,
          company_name,
          website,
          business_type,
          funding_status,
          business_stage,
          industry,
          other_industry,
          running_ads,
          ads_effective,
          ad_budget,
          marketing_management,
          decision_makers,
          implementation_timeline,
          marketing_areas,
          marketing_challenges,
          session_outcomes,
          materials_to_share,
          other_materials,
          additional_info,
          current_step
        ) VALUES (
          ${email},
          ${leadData.first_name || null},
          ${leadData.last_name || null},
          ${leadData.company_name || null},
          ${leadData.website || null},
          ${leadData.business_type || null},
          ${leadData.funding_status || null},
          ${leadData.business_stage || null},
          ${leadData.industry || null},
          ${leadData.other_industry || null},
          ${leadData.running_ads || null},
          ${leadData.ads_effective || null},
          ${leadData.ad_budget || null},
          ${leadData.marketing_management || null},
          ${leadData.decision_makers || null},
          ${leadData.implementation_timeline || null},
          ${leadData.marketing_areas || null},
          ${leadData.marketing_challenges || null},
          ${leadData.session_outcomes || null},
          ${leadData.materials_to_share || null},
          ${leadData.other_materials || null},
          ${leadData.additional_info || null},
          ${leadData.current_step || 1}
        )
        RETURNING *
      `;

      return res.status(201).json({
        success: true,
        message: 'Lead oprettet succesfuldt',
        lead: result[0],
        isNew: true
      });
    }
  } catch (error) {
    console.error('Database error in booking:', error);
    throw error;
  }
}

async function handleGetLead(req: VercelRequest, res: VercelResponse) {
  const { email } = req.query;

  if (!email || typeof email !== 'string') {
    return res.status(400).json({ error: 'E-mail parameter er påkrævet' });
  }

  try {
    const result = await sql`
      SELECT * FROM leads WHERE email = ${email}
    `;

    if (result.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Lead ikke fundet' 
      });
    }

    return res.status(200).json({
      success: true,
      lead: result[0]
    });
  } catch (error) {
    console.error('Database error in get lead:', error);
    throw error;
  }
}
