// Calendar types for booking system

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  attendees: {
    email: string;
    name: string;
  }[];
  location?: string;
  meetingUrl?: string;
}

export interface BookingDetails {
  // User information
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  
  // Session details
  sessionDate: string;
  sessionGoals: string;
  specificFocus?: string;
  
  // Business context
  primaryChallenge: string;
  marketingBudget: string;
  marketingSetup: string;
  website: string;
  challengeDescription: string;
  
  // Booking metadata
  bookingId?: string;
  calendarEventId?: string;
  paymentIntentId?: string;
  invoiceId?: string;
}

export interface CalendarAvailability {
  date: Date;
  slots: {
    startTime: string;
    endTime: string;
    available: boolean;
  }[];
}