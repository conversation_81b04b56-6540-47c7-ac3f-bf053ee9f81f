// Cal.com integration for booking sessions
// Documentation: https://cal.com/docs/api-reference/v1

export interface CalComConfig {
  username: string;
  eventTypeSlug: string;
  apiKey?: string; // Optional for public event types
}

export interface TimeSlot {
  date: Date;
  available: boolean;
  startTime: string;
  endTime: string;
}

export interface BookingData {
  name: string;
  email: string;
  date: string;
  timeZone: string;
  metadata?: Record<string, any>;
  customInputs?: Record<string, any>;
}

export class CalComIntegration {
  private config: CalComConfig;
  private baseUrl = 'https://api.cal.com/v1';

  constructor(config: CalComConfig) {
    this.config = config;
  }

  /**
   * Get the Cal.com embed URL for the booking widget
   */
  getEmbedUrl(): string {
    return `https://cal.com/${this.config.username}/${this.config.eventTypeSlug}`;
  }

  /**
   * Get available time slots for a given date range
   * Note: This requires API key for private event types
   */
  async getAvailability(startDate: Date, endDate: Date): Promise<TimeSlot[]> {
    if (!this.config.apiKey) {
      throw new Error('API key required for fetching availability');
    }

    const params = new URLSearchParams({
      username: this.config.username,
      eventTypeSlug: this.config.eventTypeSlug,
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });

    const response = await fetch(
      `${this.baseUrl}/availability?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch availability: ${response.statusText}`);
    }

    const data = await response.json();
    return this.parseAvailabilityData(data);
  }

  /**
   * Create a booking via API
   * Note: This requires API key
   */
  async createBooking(bookingData: BookingData): Promise<any> {
    if (!this.config.apiKey) {
      throw new Error('API key required for creating bookings');
    }

    const response = await fetch(`${this.baseUrl}/bookings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        eventTypeSlug: this.config.eventTypeSlug,
        username: this.config.username,
        ...bookingData,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create booking: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Parse availability data from Cal.com API response
   */
  private parseAvailabilityData(data: any): TimeSlot[] {
    const slots: TimeSlot[] = [];
    
    if (data.slots) {
      Object.entries(data.slots).forEach(([date, times]: [string, any]) => {
        times.forEach((time: any) => {
          slots.push({
            date: new Date(time.time),
            available: true,
            startTime: time.time,
            endTime: new Date(new Date(time.time).getTime() + 2 * 60 * 60 * 1000).toISOString(),
          });
        });
      });
    }

    return slots;
  }

  /**
   * Generate Cal.com embed script
   */
  generateEmbedScript(): string {
    return `
      <script>
        (function (C, A, L) {
          let p = function (a, ar) {
            a.q.push(ar);
          };
          let d = C.document;
          C.Cal = C.Cal || function () {
            let cal = C.Cal;
            let ar = arguments;
            if (!cal.loaded) {
              cal.ns = {};
              cal.q = cal.q || [];
              d.head.appendChild(d.createElement("script")).src = A;
              cal.loaded = true;
            }
            if (ar[0] === L) {
              const api = function () {
                p(api, arguments);
              };
              const namespace = ar[1];
              api.q = api.q || [];
              typeof namespace === "string" ? (cal.ns[namespace] = api) && p(api, ar) : p(cal, ar);
              return;
            }
            p(cal, ar);
          };
        })(window, "https://app.cal.com/embed/embed.js", "init");
        Cal("init");
      </script>
    `;
  }
}

// Helper function to create a Cal.com integration instance
export function createCalComIntegration(username: string, eventTypeSlug: string, apiKey?: string): CalComIntegration {
  return new CalComIntegration({
    username,
    eventTypeSlug,
    apiKey,
  });
}

// Default configuration for the marketing therapy sessions
export const defaultCalComConfig: CalComConfig = {
  username: process.env.VITE_CALCOM_USERNAME || 'asger',
  eventTypeSlug: process.env.VITE_CALCOM_EVENT_SLUG || 'marketing-therapy-session',
  apiKey: process.env.VITE_CALCOM_API_KEY,
};