// Google Calendar appointment scheduling integration
// Documentation: https://developers.google.com/calendar/api/guides/appointment-schedules

export interface GoogleCalendarConfig {
  appointmentScheduleId?: string;
  embedType: 'button' | 'inline';
  buttonLabel?: string;
  buttonColor?: string;
  width?: string;
  height?: string;
}

export interface AppointmentData {
  name: string;
  email: string;
  phone?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export class GoogleCalendarIntegration {
  private config: GoogleCalendarConfig;

  constructor(config: GoogleCalendarConfig) {
    this.config = {
      embedType: 'inline',
      buttonLabel: 'Book Marketing Therapy Session',
      buttonColor: '#000000',
      width: '100%',
      height: '600px',
      ...config
    };
  }

  /**
   * Generate the embed code for Google Calendar appointment scheduling
   */
  generateEmbedCode(appointmentLink: string): string {
    if (this.config.embedType === 'button') {
      return this.generateButtonEmbed(appointmentLink);
    }
    return this.generateInlineEmbed(appointmentLink);
  }

  /**
   * Generate button embed code
   */
  private generateButtonEmbed(appointmentLink: string): string {
    return `
      <!-- Google Calendar Appointment Scheduling begin -->
      <link href="https://calendar.google.com/calendar/scheduling-button-script.css" rel="stylesheet">
      <script src="https://calendar.google.com/calendar/scheduling-button-script.js" async></script>
      <script>
      (function() {
        var target = document.currentScript;
        window.addEventListener('load', function() {
          calendar.schedulingButton.load({
            url: '${appointmentLink}',
            color: '${this.config.buttonColor}',
            label: '${this.config.buttonLabel}',
            target,
          });
        });
      })();
      </script>
      <!-- end Google Calendar Appointment Scheduling -->
    `;
  }

  /**
   * Generate inline embed code
   */
  private generateInlineEmbed(appointmentLink: string): string {
    return `
      <iframe 
        src="${appointmentLink}" 
        style="border: 0" 
        width="${this.config.width}" 
        height="${this.config.height}" 
        frameborder="0">
      </iframe>
    `;
  }

  /**
   * Get the appointment scheduling URL
   */
  getAppointmentUrl(): string {
    const baseUrl = 'https://calendar.google.com/calendar/appointments/schedules';
    if (this.config.appointmentScheduleId) {
      return `${baseUrl}/${this.config.appointmentScheduleId}`;
    }
    // Fallback to environment variable
    return import.meta.env.VITE_GOOGLE_CALENDAR_APPOINTMENT_URL || baseUrl;
  }

  /**
   * Generate script for dynamic embedding
   */
  generateDynamicEmbedScript(): string {
    return `
      function loadGoogleCalendarAppointment(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const iframe = document.createElement('iframe');
        iframe.src = '${this.getAppointmentUrl()}';
        iframe.style.border = '0';
        iframe.style.width = '${this.config.width}';
        iframe.style.height = '${this.config.height}';
        iframe.frameBorder = '0';
        
        container.appendChild(iframe);
      }
    `;
  }
}

// Helper function to create a Google Calendar integration instance
export function createGoogleCalendarIntegration(config?: Partial<GoogleCalendarConfig>): GoogleCalendarIntegration {
  return new GoogleCalendarIntegration(config || {});
}

// Default configuration for the marketing therapy sessions
export const defaultGoogleCalendarConfig: GoogleCalendarConfig = {
  appointmentScheduleId: import.meta.env.VITE_GOOGLE_CALENDAR_SCHEDULE_ID,
  embedType: 'inline',
  buttonLabel: 'Book din Marketing Therapy Session',
  buttonColor: '#000000',
  width: '100%',
  height: '650px'
};