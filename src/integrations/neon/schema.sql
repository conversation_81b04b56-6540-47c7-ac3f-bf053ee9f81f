-- Database schema for Asger.me website
-- Run this in your Neon database console to create the required tables

-- Leads table for booking system
CREATE TABLE IF NOT EXISTS leads (
  id SERIAL PRIMARY KEY,
  first_name <PERSON><PERSON><PERSON><PERSON>(255),
  last_name <PERSON><PERSON><PERSON><PERSON>(255),
  email VARCHAR(255) UNIQUE NOT NULL,
  company_name <PERSON><PERSON><PERSON><PERSON>(255),
  website VARCHAR(255),
  business_type VARCHAR(100),
  funding_status VARCHAR(100),
  business_stage VARCHAR(100),
  industry VARCHAR(255),
  other_industry TEXT,
  running_ads VARCHAR(50),
  ads_effective VARCHAR(50),
  ad_budget VARCHAR(100),
  marketing_management VARCHAR(100),
  decision_makers TEXT[],
  implementation_timeline VARCHAR(100),
  marketing_areas TEXT[],
  marketing_challenges TEXT,
  session_outcomes TEXT,
  materials_to_share TEXT[],
  other_materials TEXT,
  additional_info TEXT,
  ai_summary TEXT,
  booking_session_id VARCHAR(255),
  current_step INTEGER DEFAULT 1,
  is_completed BOOL<PERSON><PERSON> DEFAULT FALSE,
  converted_to_customer BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contact form submissions
CREATE TABLE IF NOT EXISTS contact_submissions (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  company VARCHAR(255),
  message TEXT NOT NULL,
  source VARCHAR(100) DEFAULT 'contact_form',
  status VARCHAR(50) DEFAULT 'new',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Newsletter subscriptions
CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255),
  source VARCHAR(100) DEFAULT 'website',
  status VARCHAR(50) DEFAULT 'active',
  subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  unsubscribed_at TIMESTAMP WITH TIME ZONE
);

-- Analytics events (optional - for custom tracking)
CREATE TABLE IF NOT EXISTS analytics_events (
  id SERIAL PRIMARY KEY,
  event_name VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  session_id VARCHAR(255),
  properties JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON leads(created_at);
CREATE INDEX IF NOT EXISTS idx_leads_current_step ON leads(current_step);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscriptions(email);
CREATE INDEX IF NOT EXISTS idx_analytics_events_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);

-- Update trigger for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contact_submissions_updated_at BEFORE UPDATE ON contact_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Notifications log for Slack and other notifications
CREATE TABLE IF NOT EXISTS notifications_log (
  id SERIAL PRIMARY KEY,
  type VARCHAR(100) NOT NULL,
  payload JSONB,
  sent_to_slack BOOLEAN DEFAULT FALSE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for notifications log
CREATE INDEX IF NOT EXISTS idx_notifications_log_type ON notifications_log(type);
CREATE INDEX IF NOT EXISTS idx_notifications_log_created_at ON notifications_log(created_at);
