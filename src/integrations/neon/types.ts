// Database types for Asger.me Neon PostgreSQL database

export interface Lead {
  id: number;
  first_name?: string;
  last_name?: string;
  email: string;
  company_name?: string;
  website?: string;
  business_type?: string;
  funding_status?: string;
  business_stage?: string;
  industry?: string;
  other_industry?: string;
  running_ads?: string;
  ads_effective?: string;
  ad_budget?: string;
  marketing_management?: string;
  decision_makers?: string[];
  implementation_timeline?: string;
  marketing_areas?: string[];
  marketing_challenges?: string;
  session_outcomes?: string;
  materials_to_share?: string[];
  other_materials?: string;
  additional_info?: string;
  current_step: number;
  is_completed: boolean;
  converted_to_customer: boolean;
  created_at: string;
  updated_at: string;
}

export interface ContactSubmission {
  id: number;
  name: string;
  email: string;
  company?: string;
  message: string;
  source: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface NewsletterSubscription {
  id: number;
  email: string;
  name?: string;
  source: string;
  status: string;
  subscribed_at: string;
  unsubscribed_at?: string;
}

export interface AnalyticsEvent {
  id: number;
  event_name: string;
  user_id?: string;
  session_id?: string;
  properties?: Record<string, any>;
  created_at: string;
}

export interface DatabaseError {
  message: string;
  code?: string;
  details?: string;
}

// Table structure
export interface Tables {
  leads: Lead;
  contact_submissions: ContactSubmission;
  newsletter_subscriptions: NewsletterSubscription;
  analytics_events: AnalyticsEvent;
}

// Helper types for database operations
export type DatabaseResult<T> = T[] | DatabaseError;

// Form data types
export interface ContactFormData {
  name: string;
  email: string;
  company?: string;
  message: string;
}

export interface BookingFormData extends Partial<Lead> {
  email: string;
}
