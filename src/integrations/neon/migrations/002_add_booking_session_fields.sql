-- Migration: Add booking_session_id and ai_summary to leads table
-- Date: 2025-07-30

-- Add booking_session_id column
ALTER TABLE leads 
ADD COLUMN IF NOT EXISTS booking_session_id VARCHAR(255);

-- Add ai_summary column
ALTER TABLE leads 
ADD COLUMN IF NOT EXISTS ai_summary TEXT;

-- Add index for booking_session_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_leads_booking_session_id ON leads(booking_session_id);