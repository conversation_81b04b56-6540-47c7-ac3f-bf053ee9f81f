import 'i18next';

// Import the Danish translations as the source of truth for TypeScript
import type common from '../resources/da/common.json';
import type home from '../resources/da/home.json';
import type about from '../resources/da/about.json';
import type services from '../resources/da/services.json';
import type contact from '../resources/da/contact.json';
import type booking from '../resources/da/booking.json';

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: {
      common: typeof common;
      home: typeof home;
      about: typeof about;
      services: typeof services;
      contact: typeof contact;
      booking: typeof booking;
    };
  }
}
