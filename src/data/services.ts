import {
  Palette,
  Code,
  Smartphone,
  Search,
  BarChart3,
  Rocket,
  Globe,
  ShoppingCart,
  Users,
  Mic,
  BookOpen,
  Target,
  Zap,
  TrendingUp
} from 'lucide-react';
import i18n from '../i18n/config';

export type ServiceType = 'one-off' | 'consultation';

export interface Service {
  id: string;
  title: string;
  shortDescription: string;
  fullDescription: string;
  price: number | 'custom';
  currency: string;
  priceNote?: string;
  deliveryTime: string;
  type: ServiceType;
  category: 'design' | 'development' | 'marketing' | 'consulting';
  icon: any;
  features: string[];
  includes: string[];
  process: string[];
  testimonial?: {
    quote: string;
    author: string;
    company: string;
    rating: number;
  };
  gallery?: string[];
  popular?: boolean;
  color: string;
  bookingInfo?: {
    minAdvanceHours: number;
    paymentMethod: 'Stripe' | 'Invoice';
    bookingMethod: 'Cal.com' | 'Email';
    requiresPreInfo: boolean;
  };
}

export const services: Service[] = [
  {
    id: 'marketing-strategy-consultation',
    title: 'Marketing Terapi Session',
    shortDescription: 'Marketing terapi for hverdagsudfordringer - uden drama og BS',
    fullDescription: '<PERSON><PERSON><PERSON>, du har nogle marketing udfordringer, jeg har 10+ års erfaring med at løse dem. Vi snakker sammen i 2 timer, og du går derfra med en plan der faktisk virker. Ingen fancy slides - bare konkrete ting du kan gøre.',
    price: 3000,
    currency: 'DKK',
    priceNote: 'ekskl. moms',
    deliveryTime: '2 timer',
    type: 'consultation',
    category: 'consulting',
    icon: Target,
    color: '#FFC107',
    popular: true,
    features: [
      'Konkret marketing analyse af dit setup',
      'Plan der faktisk giver mening for dit budget',
      'Ingen BS eller fancy corporate ord',
      'Ting du kan gøre i morgen (ikke om 6 måneder)',
      'Opfølgning hvis du sidder fast'
    ],
    includes: [
      '2 timer hvor vi snakker om dit setup',
      'Jeg kigger på dit nuværende marketing før vi mødes',
      'Skriftlig opsummering af hvad vi snakkede om',
      'Konkret action plan med prioriteter',
      'Opfølgning efter 2 uger hvis du har brug for det'
    ],
    process: [
      'Du fortæller mig om din virksomhed',
      'Jeg kigger på dit setup og forbereder mig',
      'Vi snakker sammen i 2 timer',
      'Du får en skriftlig plan der giver mening',
      'Opfølgning hvis du sidder fast'
    ],
    testimonial: {
      quote: "Asger's strategic insights helped us increase our lead generation by 60% in just 3 months!",
      author: "Marcus Johnson",
      company: "GrowthTech Solutions",
      rating: 5
    },
    bookingInfo: {
      minAdvanceHours: 24,
      paymentMethod: 'Stripe',
      bookingMethod: 'Cal.com',
      requiresPreInfo: true
    }
  },
  {
    id: 'growth-strategy-intensive',
    title: 'Growth Strategy Deep Dive',
    shortDescription: '3 timers intensiv session hvor vi får styr på dit growth setup',
    fullDescription: 'En omfattende 3-timers intensiv session hvor vi kortlægger din growth strategi, identificerer flaskehalse, og laver en detaljeret action plan. Ingen BS - bare konkrete ting der virker.',
    price: 799,
    currency: 'USD',
    deliveryTime: '3 timer',
    type: 'consultation',
    category: 'consulting',
    icon: TrendingUp,
    color: '#2196F3',
    features: [
      'Growth opportunity mapping der giver mening',
      'Flaskehals identifikation',
      'Kanal optimering strategi',
      'Scaling roadmap du faktisk kan følge',
      'Implementation timeline'
    ],
    includes: [
      '3-timers intensiv session',
      'Pre-session business analyse',
      'Growth strategy dokument',
      'Action plan med timelines',
      '30-dages implementation support'
    ],
    process: [
      'Business assessment spørgeskema',
      'Pre-session analyse',
      'Intensiv strategi session',
      'Strategi dokumentation',
      'Implementation kickoff'
    ]
  },
  {
    id: 'marketing-mentorship',
    title: 'Marketing Mentorship Program',
    shortDescription: 'Ongoing guidance til marketing professionals der vil accelerere',
    fullDescription: 'Et struktureret mentorship program designet til marketing professionals og entrepreneurs der vil accelerere deres growth med personlig guidance og accountability. Pyt-skidt attitude og praktisk hjælp.',
    price: 1999,
    currency: 'USD',
    deliveryTime: '3 måneder',
    type: 'consultation',
    category: 'consulting',
    icon: BookOpen,
    color: '#9C27B0',
    features: [
      'Månedlige 1-on-1 sessions',
      'Personalized learning path der giver mening',
      'Real-time feedback og guidance',
      'Adgang til resource library',
      'Community netværk'
    ],
    includes: [
      '3 månedlige strategi sessions',
      'Email support mellem sessions',
      'Custom learning materialer',
      'Progress tracking',
      'Certificate of completion'
    ],
    process: [
      'Initial assessment og goal setting',
      'Personalized curriculum creation',
      'Månedlige mentorship sessions',
      'Progress reviews og adjustments',
      'Graduation og næste skridt'
    ]
  },
  {
    id: 'marketing-workshop',
    title: 'Marketing Workshop for Teams',
    shortDescription: 'Energy boost for kommercielle teams med praktiske eksempler',
    fullDescription: 'Hands-on workshop hvor jeg arbejder direkte med dit team. Vi får styr på jeres marketing strategi, bliver enige om prioriteter, og bygger intern kapacitet. Høj energi men afslappet vibe - vi finder ud af det sammen.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: '1-2 dage',
    type: 'consultation',
    category: 'consulting',
    icon: Users,
    color: '#2196F3',
    features: [
      'Team alignment uden corporate BS',
      'Hands-on frameworks der faktisk virker',
      'Praktiske eksempler fra rigtige cases',
      'Custom playbook I faktisk vil bruge',
      'Opfølgning så I ikke sidder fast'
    ],
    includes: [
      'Pre-workshop assessment af teamet',
      'Heldags faciliteret session med energi',
      'Custom marketing playbook der giver mening',
      'Team action plan med konkrete ting',
      '60-dages implementation support'
    ],
    process: [
      'Initial snak om jeres setup',
      'Jeg forbereder mig på jeres udfordringer',
      'Workshop dag med praktiske øvelser',
      'Vi dokumenterer hvad vi blev enige om',
      'Implementation planning og opfølgning'
    ]
  },
  {
    id: 'public-speaking',
    title: 'Oplæg & Keynotes',
    shortDescription: 'Marketing insights der faktisk giver mening - uden BS',
    fullDescription: 'Book mig til jeres næste konference eller corporate event. Jeg leverer engaging, actionable præsentationer om marketing strategi, growth tactics, og entrepreneurial mindset. Høj energi, praktiske eksempler, og ingen corporate BS.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: 'Event dato',
    type: 'consultation',
    category: 'consulting',
    icon: Mic,
    color: '#4CAF50',
    features: [
      'Customized content til jeres audience',
      'Interaktiv engagement (ikke bare slides)',
      'Real-world case studies fra rigtige virksomheder',
      'Konkrete takeaways folk faktisk kan bruge',
      'Energisk delivery med pyt-skidt attitude'
    ],
    includes: [
      'Pre-event snak om jeres audience',
      'Custom præsentation udvikling',
      'Professionel speaking engagement',
      'Resource packet til deltagerne',
      'Post-event opfølgning'
    ],
    process: [
      'Event brief og audience analyse',
      'Content customization til jeres tema',
      'Præsentation rehearsal',
      'Event delivery med energi',
      'Follow-up og feedback indsamling'
    ]
  },
  {
    id: 'marketing-audit',
    title: 'Marketing Performance Audit',
    shortDescription: 'Deep-dive analyse af dit marketing setup - hvad virker og hvad gør ikke',
    fullDescription: 'Jeg gennemgår dit marketing setup grundigt og analyserer hvad der virker, hvad der ikke gør, og hvor de største muligheder ligger. Ingen BS - bare konkrete anbefalinger.',
    price: 1299,
    currency: 'USD',
    deliveryTime: '1-2 uger',
    type: 'one-off',
    category: 'marketing',
    icon: BarChart3,
    color: '#FF5722',
    features: [
      'Komplet marketing stack analyse',
      'Competitive positioning review',
      'Performance metrics deep-dive',
      'Strategiske anbefalinger der giver mening',
      'Prioriteret action roadmap'
    ],
    includes: [
      'Omfattende audit rapport',
      'Executive summary præsentation',
      'Quick-win anbefalinger',
      '60-minutters results call',
      'Implementation templates'
    ],
    process: [
      'Access setup og data indsamling',
      'Multi-channel analyse',
      'Competitive research',
      'Rapport creation og review',
      'Results præsentation og Q&A'
    ]
  },
  {
    id: 'marketing-therapy',
    title: 'Marketing Terapi',
    shortDescription: 'For hverdagsudfordringer i teams, ledelse eller bestyrelser',
    fullDescription: 'Marketing terapi for de der situationer hvor tingene bare ikke fungerer. Hvad enten det er team udfordringer, ledelse der ikke forstår marketing, eller bestyrelser der stiller dumme spørgsmål. Vi finder ud af det sammen - uden drama.',
    price: 199,
    currency: 'USD',
    deliveryTime: '45 minutter',
    type: 'consultation',
    category: 'consulting',
    icon: Target,
    color: '#FF3366',
    features: [
      'Konkret hjælp til hverdagsudfordringer',
      'Team dynamik og kommunikation',
      'Ledelse og bestyrelseshåndtering',
      'Praktiske løsninger der virker',
      'Pyt-skidt attitude og perspektiv'
    ],
    includes: [
      '45-minutters terapi session',
      'Konkrete råd til din situation',
      'Follow-up email med key points',
      'Adgang til relevante templates',
      'Mulighed for opfølgning'
    ],
    process: [
      'Du fortæller hvad der driller',
      'Vi snakker om situationen',
      'Jeg giver konkrete råd',
      'Du får en plan for næste skridt',
      'Opfølgning hvis nødvendigt'
    ]
  },
  {
    id: 'ai-chatgpt-tools',
    title: 'AI/ChatGPT Tools & Frameworks',
    shortDescription: 'Værktøjer og frameworks der faktisk virker - bygget siden november 2022',
    fullDescription: 'Som AI/ChatGPT early adopter siden november 2022 har jeg bygget tools og frameworks der faktisk giver mening. Ikke de der crazy automations, men praktiske løsninger du kan bruge i morgen.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: 'Custom',
    type: 'consultation',
    category: 'consulting',
    icon: Zap,
    color: '#9C27B0',
    features: [
      'Custom AI tools til dit setup',
      'ChatGPT frameworks der virker',
      'Praktisk implementation',
      'Training af dit team',
      'Ongoing support og updates'
    ],
    includes: [
      'Assessment af dine behov',
      'Custom tool udvikling',
      'Implementation og setup',
      'Team training session',
      'Dokumentation og support'
    ],
    process: [
      'Vi snakker om hvad du har brug for',
      'Jeg designer løsningen',
      'Udvikling og test',
      'Implementation og training',
      'Support og optimering'
    ]
  },
  {
    id: 'undervisning-workshops',
    title: 'Undervisning & Workshops',
    shortDescription: 'Energy boosts for kommercielle teams med praktiske eksempler',
    fullDescription: 'Undervisning, workshops og oplæg der giver energi til kommercielle teams. Praktiske eksempler fra rigtige cases, høj energi men afslappet vibe. Ingen PowerPoint-koma - bare ting I faktisk kan bruge.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: 'Halv eller hel dag',
    type: 'consultation',
    category: 'consulting',
    icon: BookOpen,
    color: '#FF9800',
    features: [
      'Customized til jeres team og udfordringer',
      'Praktiske øvelser og eksempler',
      'Høj energi og engagement',
      'Konkrete takeaways',
      'Follow-up materialer'
    ],
    includes: [
      'Pre-workshop team assessment',
      'Customized workshop content',
      'Faciliteret session med energi',
      'Praktiske øvelser og cases',
      'Follow-up materialer og support'
    ],
    process: [
      'Vi snakker om jeres behov',
      'Jeg designer workshop til jer',
      'Workshop dag med praktiske øvelser',
      'Opsamling og action points',
      'Follow-up og materialer'
    ]
  }
];

export const getServiceById = (id: string): Service | undefined => {
  return services.find(service => service.id === id);
};

// Function to get service with translations applied
export const getTranslatedServiceById = (id: string): Service | undefined => {
  const baseService = getServiceById(id);
  if (!baseService) return undefined;

  // Map service IDs to translation keys
  const serviceTranslationKeys: Record<string, string> = {
    'marketing-strategy-consultation': 'marketingTherapySession',
    'marketing-workshop': 'marketingWorkshop',
    'website-audit': 'websiteAudit',
    'conversion-optimization': 'conversionOptimization',
    'marketing-automation': 'marketingAutomation',
    'content-strategy': 'contentStrategy',
    'social-media-strategy': 'socialMediaStrategy',
    'email-marketing': 'emailMarketing',
    'ppc-management': 'ppcManagement',
    'seo-optimization': 'seoOptimization',
    'brand-identity': 'brandIdentity',
    'website-design': 'websiteDesign',
    'landing-page': 'landingPage',
    'undervisning-workshops': 'undervisningWorkshops'
  };

  const translationKey = serviceTranslationKeys[id];
  if (!translationKey) return baseService;

  try {
    // Get translated content
    const t = i18n.getFixedT(i18n.language, 'services');

    return {
      ...baseService,
      title: t(`services.${translationKey}.title`, baseService.title),
      shortDescription: t(`services.${translationKey}.shortDescription`, baseService.shortDescription),
      fullDescription: t(`services.${translationKey}.fullDescription`, baseService.fullDescription),
      deliveryTime: t(`services.${translationKey}.deliveryTime`, baseService.deliveryTime),
      features: t(`services.${translationKey}.features`, { returnObjects: true, defaultValue: baseService.features }) as string[],
      includes: t(`services.${translationKey}.includes`, { returnObjects: true, defaultValue: baseService.includes }) as string[],
      process: t(`services.${translationKey}.process`, { returnObjects: true, defaultValue: baseService.process }) as string[],
    };
  } catch (error) {
    console.warn('Translation error for service:', id, error);
    return baseService;
  }
};

// Function to get all services with translations applied
export const getTranslatedServices = (): Service[] => {
  return services.map(service => getTranslatedServiceById(service.id) || service);
};

export const getServicesByCategory = (category: string): Service[] => {
  return services.filter(service => service.category === category);
};

export const getServicesByType = (type: ServiceType): Service[] => {
  return services.filter(service => service.type === type);
};
