export interface NavigationLink {
  href: string;
  labelKey: string; // Translation key instead of hardcoded label
  external?: boolean;
}

export const navigationLinks: NavigationLink[] = [
  { href: '/', labelKey: 'navigation.home' },
  // { href: '/services', labelKey: 'navigation.services' }, // Hidden for launch
  { href: '/about', labelKey: 'navigation.about' },
  { href: '/experience', labelKey: 'navigation.experience' },
  { href: '/#testimonials', labelKey: 'navigation.testimonials' },
  { href: '/contact', labelKey: 'navigation.contact' },
];

export const ctaButton = {
  href: '/booking',
  labelKey: 'navigation.bookStrategyCall',
};
