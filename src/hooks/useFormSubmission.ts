import { useState } from 'react';
import { submitContactForm as apiSubmitContactForm, createOrUpdateLead, subscribeToNewsletter } from '@/services/api';
import type { ContactFormData, BookingFormData } from '@/integrations/neon/types';
import { useAnalytics } from '@/services/analytics';

interface FormSubmissionState {
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
  data: any | null;
}

export function useFormSubmission() {
  const [state, setState] = useState<FormSubmissionState>({
    isLoading: false,
    isSuccess: false,
    error: null,
    data: null,
  });

  const analytics = useAnalytics();

  const resetState = () => {
    setState({
      isLoading: false,
      isSuccess: false,
      error: null,
      data: null,
    });
  };

  const submitContactForm = async (formData: ContactFormData) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Track form submission attempt
      analytics.track('contact_form_submitted', {
        form_type: 'contact',
        has_company: !!formData.company,
        message_length: formData.message.length,
      });

      const result = await apiSubmitContactForm(formData);

      setState({
        isLoading: false,
        isSuccess: true,
        error: null,
        data: result,
      });

      // Track successful submission
      analytics.track('contact_form_success', {
        submission_id: result.submissionId,
        form_type: 'contact',
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit form';
      
      setState({
        isLoading: false,
        isSuccess: false,
        error: errorMessage,
        data: null,
      });

      // Track form submission error
      analytics.track('contact_form_error', {
        error_message: errorMessage,
        form_type: 'contact',
      });

      throw error;
    }
  };

  const submitBookingForm = async (formData: BookingFormData) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Track booking form submission
      analytics.track('booking_form_submitted', {
        form_type: 'booking',
        current_step: formData.current_step || 1,
        has_company: !!formData.company_name,
        business_type: formData.business_type,
      });

      const result = await createOrUpdateLead(formData);

      setState({
        isLoading: false,
        isSuccess: true,
        error: null,
        data: result,
      });

      // Track successful booking submission
      analytics.track('booking_form_success', {
        lead_id: result.lead.id,
        is_new_lead: result.isNew,
        current_step: result.lead.current_step,
        form_type: 'booking',
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit booking form';
      
      setState({
        isLoading: false,
        isSuccess: false,
        error: errorMessage,
        data: null,
      });

      // Track booking form error
      analytics.track('booking_form_error', {
        error_message: errorMessage,
        form_type: 'booking',
      });

      throw error;
    }
  };

  const submitNewsletterSubscription = async (email: string, name?: string, source?: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Track newsletter subscription attempt
      analytics.track('newsletter_subscription_attempted', {
        source: source || 'website',
        has_name: !!name,
      });

      const result = await subscribeToNewsletter(email, name, source);

      setState({
        isLoading: false,
        isSuccess: true,
        error: null,
        data: result,
      });

      // Track successful subscription
      analytics.track('newsletter_subscription_success', {
        subscription_id: result.subscriptionId,
        already_subscribed: result.alreadySubscribed,
        reactivated: result.reactivated,
        source: source || 'website',
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe to newsletter';
      
      setState({
        isLoading: false,
        isSuccess: false,
        error: errorMessage,
        data: null,
      });

      // Track newsletter subscription error
      analytics.track('newsletter_subscription_error', {
        error_message: errorMessage,
        source: source || 'website',
      });

      throw error;
    }
  };

  return {
    ...state,
    submitContactForm,
    submitBookingForm,
    submitNewsletterSubscription,
    resetState,
  };
}

// Specialized hooks for specific forms
export function useContactForm() {
  const { submitContactForm, ...rest } = useFormSubmission();
  return { submitForm: submitContactForm, ...rest };
}

export function useBookingForm() {
  const { submitBookingForm, ...rest } = useFormSubmission();
  return { submitForm: submitBookingForm, ...rest };
}

export function useNewsletterForm() {
  const { submitNewsletterSubscription, ...rest } = useFormSubmission();
  return { submitForm: submitNewsletterSubscription, ...rest };
}
