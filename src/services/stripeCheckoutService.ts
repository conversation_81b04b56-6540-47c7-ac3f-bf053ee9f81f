import { FormData } from '../contexts/FormContext';

export interface StripeCheckoutData {
  formData: FormData;
  serviceId: string;
  priceInDKK: number;
}

/**
 * Create a Stripe Checkout session for consultation booking
 */
export const createConsultationCheckout = async (data: StripeCheckoutData) => {
  try {
    const response = await fetch('/api/create-consultation-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        formData: data.formData,
        serviceId: data.serviceId,
        priceInDKK: data.priceInDKK,
        successUrl: `${window.location.origin}/booking/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: window.location.href,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create checkout session');
    }

    const { checkoutUrl } = await response.json();
    
    // Redirect to Stripe Checkout
    window.location.href = checkoutUrl;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
};

/**
 * Mock checkout for development
 */
export const mockCreateConsultationCheckout = async (data: StripeCheckoutData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // In development, redirect to success page with mock session ID
  const mockSessionId = 'cs_test_' + Math.random().toString(36).substr(2, 9);
  window.location.href = `/booking/success?session_id=${mockSessionId}&mock=true`;
};