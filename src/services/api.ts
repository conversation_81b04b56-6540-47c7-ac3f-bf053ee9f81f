// API service for interacting with backend endpoints
import type { ContactFormData, BookingFormData, Lead } from '@/integrations/neon/types';

const API_BASE_URL = import.meta.env.PROD ? '' : 'http://localhost:3000';

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}/api${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// Contact form API
export interface ContactSubmissionResponse {
  success: boolean;
  message: string;
  submissionId: number;
  timestamp: string;
}

export async function submitContactForm(data: ContactFormData): Promise<ContactSubmissionResponse> {
  return apiRequest<ContactSubmissionResponse>('/contact', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// Booking/Lead API
export interface LeadResponse {
  success: boolean;
  message: string;
  lead: Lead;
  isNew?: boolean;
}

export async function createOrUpdateLead(data: BookingFormData): Promise<LeadResponse> {
  return apiRequest<LeadResponse>('/booking', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function getLead(email: string): Promise<LeadResponse> {
  return apiRequest<LeadResponse>(`/booking?email=${encodeURIComponent(email)}`, {
    method: 'GET',
  });
}

// Newsletter API
export interface NewsletterSubscriptionResponse {
  success: boolean;
  message: string;
  subscriptionId: number;
  timestamp?: string;
  alreadySubscribed?: boolean;
  reactivated?: boolean;
}

export async function subscribeToNewsletter(
  email: string,
  name?: string,
  source?: string
): Promise<NewsletterSubscriptionResponse> {
  return apiRequest<NewsletterSubscriptionResponse>('/newsletter', {
    method: 'POST',
    body: JSON.stringify({ email, name, source }),
  });
}

// Analytics API (for custom events)
export interface AnalyticsEventData {
  event_name: string;
  user_id?: string;
  session_id?: string;
  properties?: Record<string, any>;
}

export async function trackCustomEvent(data: AnalyticsEventData): Promise<{ success: boolean }> {
  try {
    return apiRequest<{ success: boolean }>('/analytics', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  } catch (error) {
    // Don't throw for analytics errors, just log them
    console.warn('Analytics tracking failed:', error);
    return { success: false };
  }
}

// Error handling utilities
export class APIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Retry utility for failed requests
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError!;
}

// Health check endpoint
export async function healthCheck(): Promise<{ status: string; timestamp: string }> {
  return apiRequest<{ status: string; timestamp: string }>('/health', {
    method: 'GET',
  });
}
