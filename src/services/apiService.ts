
// API imports removed - using real server endpoints

// Set up fetch interceptor for API calls
export const setupApiInterceptor = () => {
  const originalFetch = window.fetch;
  
  window.fetch = async function(input, init) {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : input.url;
    
    // Pass through all API calls - we now have real server endpoints
    
    // Pass through to original fetch for all other calls
    return originalFetch.apply(this, [input, init]);
  };
};
