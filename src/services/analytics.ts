import { usePostHog } from 'posthog-js/react';
import { addBreadcrumb, reportError } from '../utils/errorTracking';

// Event types for type safety
export interface AnalyticsEvents {
  // Page views
  'page_viewed': {
    page_name: string;
    path: string;
    referrer?: string;
  };

  // Booking form events
  'booking_form_started': {
    source?: string;
  };
  'booking_form_step_completed': {
    step: number;
    step_name: string;
  };
  'booking_form_step_abandoned': {
    step: number;
    step_name: string;
    time_spent_seconds?: number;
  };
  'booking_form_completed': {
    total_time_seconds?: number;
    lead_id?: string;
  };
  'booking_form_error': {
    step: number;
    error_type: string;
    error_message?: string;
  };

  // Contact form events
  'contact_form_started': {
    source?: string;
  };
  'contact_form_submitted': {
    has_company: boolean;
    message_length: number;
  };
  'contact_form_error': {
    error_type: string;
    error_message?: string;
  };

  // Service checkout events
  'service_viewed': {
    service_id: string;
    service_name: string;
    service_price?: number;
  };
  'service_checkout_started': {
    service_id: string;
    service_name: string;
    service_price?: number;
  };
  'service_checkout_completed': {
    service_id: string;
    service_name: string;
    service_price?: number;
    payment_method?: string;
  };
  'service_checkout_abandoned': {
    service_id: string;
    service_name: string;
    step: string;
  };

  // User interaction events
  'cta_clicked': {
    cta_text: string;
    cta_location: string;
    destination: string;
  };
  'navigation_clicked': {
    link_text: string;
    destination: string;
    source_page: string;
  };
  'website_analyzed': {
    website_url: string;
    analysis_successful: boolean;
    company_detected?: string;
    industry_detected?: string;
  };
  'external_redirect': {
    destination: string;
    source_page: string;
    context?: string;
  };

  // Engagement events
  'section_viewed': {
    section_name: string;
    page: string;
  };
  'testimonial_viewed': {
    testimonial_id?: string;
  };
  'pricing_viewed': {
    pricing_section: string;
  };
  
  // Newsletter events
  'newsletter_popup_shown': {
    trigger: string;
    seconds_on_site: number;
  };
  'newsletter_popup_closed': {
    seconds_shown: number;
    action: string;
  };
  'newsletter_subscribed': {
    source: string;
    seconds_on_site?: number;
    has_name: boolean;
  };
  'newsletter_subscription_error': {
    error: string;
    source: string;
  };
  
  // Checkout events
  'checkout_started': {
    service: string;
    price_dkk: number;
    source_page: string;
  };
}

// Analytics service class
export class AnalyticsService {
  public readonly posthog: any; // Made public readonly for singleton comparison
  private eventQueue: Map<string, number[]> = new Map();
  private readonly THROTTLE_WINDOW = 10000; // 10 seconds
  private readonly MAX_EVENTS_PER_WINDOW = 5; // Very conservative limit
  private readonly SAME_EVENT_COOLDOWN = 2000; // 2 seconds between same events

  constructor(posthog: any) {
    this.posthog = posthog;
  }

  // More aggressive throttling to prevent rate limiting
  private shouldThrottleEvent(eventName: string): boolean {
    const now = Date.now();

    // Get event history for this event type
    const eventHistory = this.eventQueue.get(eventName) || [];

    // Remove old events outside the throttle window
    const recentEvents = eventHistory.filter(time => now - time < this.THROTTLE_WINDOW);

    // Check if we've hit the same event cooldown
    if (recentEvents.length > 0) {
      const lastEventTime = Math.max(...recentEvents);
      if (now - lastEventTime < this.SAME_EVENT_COOLDOWN) {
        return true; // Too soon since last same event
      }
    }

    // Check if we've hit the overall rate limit
    if (recentEvents.length >= this.MAX_EVENTS_PER_WINDOW) {
      return true; // Too many events in window
    }

    // Add this event to the history
    recentEvents.push(now);
    this.eventQueue.set(eventName, recentEvents);

    return false;
  }

  // Track events with type safety
  track<K extends keyof AnalyticsEvents>(
    event: K,
    properties: AnalyticsEvents[K]
  ): void {
    // Skip all tracking in development
    if (import.meta.env.DEV) {
      console.log(`[Analytics Dev] ${event}:`, properties);
      return;
    }

    if (!this.posthog) {
      return; // Silently fail if PostHog not initialized
    }

    // Check if we should throttle this event
    if (this.shouldThrottleEvent(event as string)) {
      return;
    }

    try {
      this.posthog.capture(event, {
        ...properties,
        timestamp: new Date().toISOString(),
        // Reduce metadata to minimize payload size
        url: window.location.pathname, // Just path, not full URL
      });
    } catch (error) {
      console.error('PostHog tracking error:', error);
    }
  }

  // Identify user (for when they provide contact info)
  identify(userId: string, properties?: Record<string, any>): void {
    if (import.meta.env.DEV) {
      console.log(`[Analytics Dev] identify:`, userId, properties);
      return;
    }
    if (!this.posthog) return;
    
    this.posthog.identify(userId, properties);
  }

  // Set user properties
  setUserProperties(properties: Record<string, any>): void {
    if (import.meta.env.DEV) {
      console.log(`[Analytics Dev] setUserProperties:`, properties);
      return;
    }
    if (!this.posthog) return;
    
    this.posthog.setPersonProperties(properties);
  }

  // Track page views
  trackPageView(pageName: string, additionalProperties?: Record<string, any>): void {
    if (import.meta.env.DEV) {
      console.log(`[Analytics Dev] Page View: ${pageName}`);
      return;
    }
    this.track('page_viewed', {
      page_name: pageName,
      path: window.location.pathname,
      referrer: document.referrer,
      ...additionalProperties,
    });
  }

  // Track errors
  trackError(error: Error, context?: Record<string, any>): void {
    try {
      if (import.meta.env.DEV) {
        console.error(`[Analytics Dev] Error:`, error, context);
        return;
      }
      if (!this.posthog) return;

      this.posthog.capture('error_occurred', {
        error_message: error.message,
        error_stack: error.stack,
        error_name: error.name,
        context,
        timestamp: new Date().toISOString(),
        url: window.location.href,
      });

      // Also report to error tracking service
      reportError(error, context);
      addBreadcrumb(`Error: ${error.message}`, 'error');
    } catch (trackingError) {
      console.error('Error tracking failed:', trackingError);
    }
  }

  // Track performance metrics
  trackPerformance(metric: string, value: number, unit: string = 'ms'): void {
    try {
      if (import.meta.env.DEV) {
        console.log(`[Analytics Dev] Performance: ${metric} = ${value}${unit}`);
        return;
      }
      if (!this.posthog) return;

      this.posthog.capture('performance_metric', {
        metric_name: metric,
        metric_value: value,
        metric_unit: unit,
        performance_now: performance.now(),
        timestamp: new Date().toISOString(),
      });

      addBreadcrumb(`Performance: ${metric} = ${value}${unit}`, 'performance');
    } catch (error) {
      console.error('Performance tracking failed:', error);
    }
  }

  // Reset user (for logout or new session)
  reset(): void {
    if (import.meta.env.DEV) {
      console.log(`[Analytics Dev] Reset user`);
      return;
    }
    if (!this.posthog) return;
    
    this.posthog.reset();
  }
}

// Singleton instance to maintain state across renders
let analyticsInstance: AnalyticsService | null = null;

// Hook to use analytics service
export const useAnalytics = () => {
  const posthog = usePostHog();

  // Create or reuse singleton analytics service instance
  if (!analyticsInstance || analyticsInstance.posthog !== posthog) {
    analyticsInstance = new AnalyticsService(posthog);
  }

  return analyticsInstance;
};

// Utility functions for common tracking patterns
export const trackFormStart = (formType: 'booking' | 'contact', source?: string) => {
  const analytics = new AnalyticsService(window.posthog);
  
  if (formType === 'booking') {
    analytics.track('booking_form_started', { source });
  } else {
    analytics.track('contact_form_started', { source });
  }
};

export const trackCTAClick = (ctaText: string, location: string, destination: string) => {
  const analytics = new AnalyticsService(window.posthog);
  analytics.track('cta_clicked', {
    cta_text: ctaText,
    cta_location: location,
    destination,
  });
};

export const trackNavigation = (linkText: string, destination: string, sourcePage: string) => {
  const analytics = new AnalyticsService(window.posthog);
  analytics.track('navigation_clicked', {
    link_text: linkText,
    destination,
    source_page: sourcePage,
  });
};

// Error tracking
export const trackError = (error: Error, context?: string) => {
  if (window.posthog) {
    window.posthog.capture('error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      context,
      url: window.location.href,
    });
  }
};
