import { FormData } from '../contexts/FormContext';

export interface AIAnalysisResult {
  companyName?: string;
  industry?: string;
  summary?: string;
}

/**
 * Generate a comprehensive AI summary based on all form data
 */
export const generateComprehensiveSummary = async (formData: FormData): Promise<string> => {
  try {
    // In production, this would call your backend API
    const response = await fetch('/api/generate-comprehensive-summary', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        formData,
        language: 'da' // Danish
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate summary');
    }

    const data = await response.json();
    return data.summary;
  } catch (error) {
    console.error('Error generating comprehensive summary:', error);
    
    // Fallback to local generation if API fails
    return generateLocalSummary(formData);
  }
};

/**
 * Analyze website with AI to extract company information
 */
export const analyzeWebsiteWithAI = async (url: string): Promise<AIAnalysisResult> => {
  try {
    const response = await fetch('/api/website-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, mode: 'full' }),
    });

    if (!response.ok) {
      throw new Error('Failed to analyze website');
    }

    return await response.json();
  } catch (error) {
    console.error('Error analyzing website:', error);
    // Return empty result on error
    return {};
  }
};

/**
 * Generate a summary locally as fallback
 */
function generateLocalSummary(formData: FormData): string {
  const businessTypeMap: Record<string, string> = {
    'startup': 'startup',
    'established_business': 'etableret virksomhed',
    'agency': 'bureau',
    'freelancer': 'freelancer/konsulent',
    'other': 'virksomhed'
  };

  const fundingMap: Record<string, string> = {
    'bootstrapped': 'bootstrapped',
    'funded': 'funded'
  };

  const stageMap: Record<string, string> = {
    'early_stage': 'early-stage',
    'growth_stage': 'growth-stage',
    'established': 'etableret',
    'enterprise': 'enterprise'
  };

  const budgetMap: Record<string, string> = {
    'less_than_5k': 'under 5.000 kr/md',
    '5k_10k': '5.000-10.000 kr/md',
    '10k_20k': '10.000-20.000 kr/md',
    '20k_50k': '20.000-50.000 kr/md',
    '50k_100k': '50.000-100.000 kr/md',
    '100k_plus': 'over 100.000 kr/md'
  };

  const timelineMap: Record<string, string> = {
    'right_now': 'har brug for hjælp nu',
    '1_3_months': 'inden for 1-3 måneder',
    'exploring': 'researcher muligheder'
  };

  let summary = `<strong>${formData.companyName}</strong> er `;

  // Business type and stage
  if (formData.businessType === 'startup') {
    summary += `en ${fundingMap[formData.fundingStatus] || ''} ${stageMap[formData.businessStage] || ''} startup`;
  } else {
    summary += `en ${businessTypeMap[formData.businessType] || 'virksomhed'}`;
  }

  // Industry
  if (formData.industry) {
    summary += ` inden for ${formData.industry === 'other' ? formData.otherIndustry : formData.industry}`;
  }

  summary += '.';

  // Marketing situation
  if (formData.runningAds === 'yes') {
    summary += ` De kører i øjeblikket annoncer med et budget på ${budgetMap[formData.adBudget] || 'ukendt beløb'}`;
    if (formData.adsEffective === 'no') {
      summary += ', men de er ikke effektive';
    } else if (formData.adsEffective === 'yes') {
      summary += ' som er effektive';
    } else {
      summary += ', men er usikre på effektiviteten';
    }
    summary += '.';
  } else {
    summary += ' De kører ikke annoncer i øjeblikket.';
  }

  // Marketing management
  const managementMap: Record<string, string> = {
    'in_house': 'in-house',
    'agency': 'gennem bureau/freelancer',
    'mix': 'med en blanding af in-house og eksterne',
    'not_managed': 'ikke aktivt'
  };
  summary += ` Marketing håndteres ${managementMap[formData.marketingManagement] || ''}.`;

  // Challenges
  if (formData.marketingChallenges) {
    summary += `<br><br><strong>Primære udfordringer:</strong><br>${formData.marketingChallenges.replace(/\n/g, '<br>')}`;
  }

  // Desired outcomes
  if (formData.sessionOutcomes) {
    summary += `<br><br><strong>Ønskede resultater fra sessionen:</strong><br>${formData.sessionOutcomes.replace(/\n/g, '<br>')}`;
  }

  // Timeline
  summary += `<br><br>De ${timelineMap[formData.implementationTimeline] || 'har ikke angivet en tidslinje'}.`;

  // Focus areas
  if (formData.marketingAreas && formData.marketingAreas.length > 0) {
    const areaLabels = formData.marketingAreas.map(area => {
      const areaMap: Record<string, string> = {
        'paid-advertising': 'betalt annoncering',
        'growth-strategy': 'growth strategi',
        'content-marketing': 'content marketing',
        'seo': 'SEO',
        'email-marketing': 'email marketing',
        'social-media': 'social media',
        'brand-strategy': 'brand strategi',
        'cro': 'conversion optimization',
        'marketing-automation': 'marketing automation'
      };
      return areaMap[area] || area;
    });
    summary += ` Fokusområder inkluderer: ${areaLabels.join(', ')}.`;
  }

  return summary;
}