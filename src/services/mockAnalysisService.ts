// Mock service for testing comprehensive analysis locally
export const mockAnalyzeWebsiteComprehensive = async (url: string) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Extract domain name from URL
  let domain = '';
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    domain = urlObj.hostname.replace('www.', '').split('.')[0];
  } catch (e) {
    domain = 'unknown';
  }

  // Generate different mock data based on domain
  const mockDataByDomain: Record<string, any> = {
    quickorder: {
      company_profile: {
        name: "QuickOrder",
        legal_name: "QuickOrder ApS",
        founded: "2019",
        size: "10-50 employees",
        locations: ["København", "Aarhus"],
        vat_number: "DK40123456"
      },
      business_model: {
        type: "B2B SaaS",
        industry: "Restaurant Technology",
        sub_industries: ["Food Service", "Digital Ordering", "Restaurant Management"],
        revenue_model: "Subscription-based with transaction fees",
        target_markets: ["Denmark", "Norway", "Sweden"],
        customer_segments: ["Restaurants", "Cafes", "Food trucks", "Ghost kitchens"]
      },
      value_proposition: "Øg din omsætning med 30% med vores digitale bestillingsløsning",
      challenges: ["Competition from established players", "Restaurant adoption of new technology", "Integration complexity with legacy systems"],
      opportunities: ["Expand to other Nordic countries", "Add delivery management features", "Loyalty program integration"]
    },
    zendesk: {
      company_profile: {
        name: "Zendesk",
        legal_name: "Zendesk Inc.",
        founded: "2007",
        size: "5000+ employees",
        locations: ["San Francisco", "Copenhagen", "Dublin", "Melbourne"],
        vat_number: "US123456789"
      },
      business_model: {
        type: "B2B SaaS",
        industry: "Customer Service Software",
        sub_industries: ["CRM", "Help Desk", "Customer Support", "AI Customer Service"],
        revenue_model: "Subscription-based pricing tiers",
        target_markets: ["Global", "Enterprise", "SMB"],
        customer_segments: ["Enterprise companies", "Growing businesses", "Startups", "E-commerce"]
      },
      value_proposition: "Customer service software that's easy to use and scales with your business",
      challenges: ["Intense competition from Salesforce and others", "Market saturation in core markets", "Need for continuous innovation"],
      opportunities: ["AI-powered customer service", "Expansion in emerging markets", "Deeper enterprise penetration"]
    },
    shopify: {
      company_profile: {
        name: "Shopify",
        legal_name: "Shopify Inc.",
        founded: "2006",
        size: "10000+ employees",
        locations: ["Ottawa", "Toronto", "New York", "London", "Berlin"],
        vat_number: "CA987654321"
      },
      business_model: {
        type: "B2B/B2C Platform",
        industry: "E-commerce Platform",
        sub_industries: ["Online Retail", "Payment Processing", "Digital Commerce", "Merchant Services"],
        revenue_model: "Subscription + transaction fees + merchant solutions",
        target_markets: ["Global", "SMB to Enterprise"],
        customer_segments: ["Online retailers", "Brick-and-mortar stores", "Dropshippers", "Creators"]
      },
      value_proposition: "The all-in-one commerce platform to start, run, and grow a business",
      challenges: ["Amazon competition", "Economic downturn impact on merchants", "Maintaining growth rates"],
      opportunities: ["B2B commerce expansion", "International market growth", "AI-powered store optimization"]
    },
    stripe: {
      company_profile: {
        name: "Stripe",
        legal_name: "Stripe, Inc.",
        founded: "2010",
        size: "8000+ employees",
        locations: ["San Francisco", "Dublin", "London", "Singapore", "Tokyo"],
        vat_number: "US456789123"
      },
      business_model: {
        type: "B2B SaaS/FinTech",
        industry: "Payment Processing",
        sub_industries: ["FinTech", "Payment Infrastructure", "Banking-as-a-Service", "Business Finance"],
        revenue_model: "Transaction-based fees",
        target_markets: ["Global", "Developers", "Enterprises"],
        customer_segments: ["Online businesses", "Marketplaces", "SaaS companies", "Platforms"]
      },
      value_proposition: "The financial infrastructure for the internet",
      challenges: ["Regulatory compliance across markets", "Competition from traditional banks", "Fraud prevention"],
      opportunities: ["Embedded finance", "Crypto payments", "Global expansion", "Banking services"]
    }
  };

  // Get mock data for domain or generate generic data
  const domainData = mockDataByDomain[domain] || {
    company_profile: {
      name: domain.charAt(0).toUpperCase() + domain.slice(1),
      legal_name: `${domain.charAt(0).toUpperCase() + domain.slice(1)} A/S`,
      founded: "2020",
      size: "1-10 employees",
      locations: ["Denmark"],
      vat_number: "DK" + Math.floor(Math.random() * *********)
    },
    business_model: {
      type: "B2B",
      industry: "Technology",
      sub_industries: ["Software", "Services"],
      revenue_model: "Project-based",
      target_markets: ["Denmark"],
      customer_segments: ["Small businesses", "Startups"]
    },
    value_proposition: `Leading ${domain} solutions for modern businesses`,
    challenges: ["Market awareness", "Competition", "Scaling operations"],
    opportunities: ["Digital transformation", "Market expansion", "Product development"]
  };

  // Build the full response
  const companyData = domainData;
  
  return {
    success: true,
    analysis: {
      company_profile: companyData.company_profile,
      business_model: companyData.business_model,
      products_services: {
        main_offerings: [
          `${companyData.company_profile.name} core platform`,
          "Professional services",
          "Customer support",
          "Analytics dashboard"
        ],
        pricing_model: "Flexible pricing based on usage",
        unique_selling_points: [
          "Industry-leading solution",
          "Local market expertise",
          "24/7 support",
          "Easy integration"
        ],
        technologies_used: ["Modern tech stack", "Cloud infrastructure", "API-first design", "Mobile-ready"]
      },
      marketing_insights: {
        value_proposition: companyData.value_proposition,
        brand_positioning: `Leading ${companyData.business_model.industry} provider in ${companyData.business_model.target_markets[0]}`,
        marketing_channels: ["Direct sales", "Content marketing", "SEO", "Social media", "Events"],
        social_proof: [
          "Trusted by leading companies",
          "Industry certifications",
          "Customer testimonials"
        ],
        case_studies: ["Increased efficiency by 40%", "Reduced costs by 30%"],
        tone_of_voice: "Professional and trustworthy"
      },
      digital_presence: {
        website_quality: "Professional",
        mobile_optimized: true,
        languages: ["Danish", "English"],
        blog_active: true,
        social_media_links: ["LinkedIn", "Facebook", "Twitter"],
        newsletter: true,
        chat_support: false
      },
      competitive_landscape: {
        competitors_mentioned: ["Industry leaders", "Local competitors"],
        market_position: "Growing",
        partnerships: ["Technology partners", "Industry associations"]
      },
      pain_points_opportunities: {
        problems_they_solve: [
          "Inefficient processes",
          "Lack of automation",
          "Poor customer experience",
          "Limited scalability"
        ],
        potential_challenges: companyData.challenges,
        growth_opportunities: companyData.opportunities
      },
      contact_readiness: {
        cta_strategy: "Contact us for a consultation",
        contact_methods: ["Contact form", "Email", "Phone"],
        response_time: "Within 48 hours",
        booking_available: true
      }
    },
    executiveSummary: generateExecutiveSummary(companyData, domain),
    additionalData: {
      emails: [`info@${domain}.com`, `support@${domain}.com`],
      phoneNumbers: ["+45 " + Math.floor(Math.random() * 90000000 + 10000000)],
      socialMedia: {
        linkedin: `linkedin.com/company/${domain}`,
        facebook: `facebook.com/${domain}`,
        instagram: `instagram.com/${domain}`
      },
      visiblePrices: ["Contact for pricing"],
      cvr: companyData.company_profile.vat_number,
      aps: "A/S"
    },
    metadata: {
      title: `${companyData.company_profile.name} - ${companyData.business_model.industry} Solutions`,
      description: companyData.value_proposition,
      ogImage: `https://${domain}.com/og-image.jpg`,
      favicon: `https://${domain}.com/favicon.ico`,
      sourceUrl: url,
      analyzedAt: new Date().toISOString()
    }
  };
};

// Helper function to generate dynamic executive summary
function generateExecutiveSummary(companyData: any, domain: string): string {
  const { company_profile, business_model, value_proposition, challenges, opportunities } = companyData;
  
  return `${company_profile.name} er en ${business_model.type} virksomhed inden for ${business_model.industry}. 
De betjener primært ${business_model.customer_segments.join(', ')} på ${business_model.target_markets.join(', ')} markederne.

Deres værdiforslag: "${value_proposition}"

Marketing terapi kunne hjælpe med at:
- ${opportunities[0]}
- ${opportunities[1]}
- Håndtere udfordringen med ${challenges[0].toLowerCase()}
- Udvikle en stærkere digital tilstedeværelse`;
}