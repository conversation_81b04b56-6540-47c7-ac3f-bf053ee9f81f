
import { WebsiteMetadata } from '../components/booking/WebsitePreview';

/**
 * Normalizes a URL by adding https:// if not present
 */
export const normalizeUrl = (url: string): string => {
  let normalizedUrl = url.trim();
  if (!normalizedUrl.startsWith('http')) {
    normalizedUrl = `https://${normalizedUrl}`;
  }
  
  // Remove trailing slashes for consistency
  return normalizedUrl.replace(/\/$/, '');
};

/**
 * Extracts favicon URL from the website domain
 */
export const getFaviconUrl = (url: string): string => {
  try {
    const domain = new URL(url).origin;
    // Use Google's favicon service which is reliable and fast
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
  } catch (e) {
    return '';
  }
};

/**
 * Fetches website metadata using OpenAI's web search API
 * with fallbacks for reliability
 */
export const fetchWebsiteMetadata = async (url: string): Promise<WebsiteMetadata> => {
  if (!url || url.length < 5) {
    throw new Error('Invalid URL');
  }

  const normalizedUrl = normalizeUrl(url);
  
  // Try multiple methods in sequence until one works
  try {
    // First try with OpenAI web search
    return await fetchWithOpenAI(normalizedUrl);
  } catch (error) {
    console.error('OpenAI metadata fetch method failed:', error);
    
    // If OpenAI fails, try with allorigins as fallback
    try {
      return await fetchWithAllOrigins(normalizedUrl);
    } catch (secondError) {
      console.error('AllOrigins fallback metadata fetch method failed:', secondError);
      
      // If that fails too, try with cors-anywhere as last resort
      try {
        return await fetchWithCorsAnywhere(normalizedUrl);
      } catch (thirdError) {
        console.error('All metadata fetch methods failed:', thirdError);
        throw new Error('Could not fetch website information. Please check the URL and try again.');
      }
    }
  }
};

/**
 * Primary method using OpenAI's web search API
 */
const fetchWithOpenAI = async (url: string): Promise<WebsiteMetadata> => {
  // Create a controller for timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10-second timeout
  
  try {
    const response = await fetch('/api/website-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, mode: 'metadata' }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch website data: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data || !data.metadata) {
      throw new Error('No metadata received from OpenAI API');
    }
    
    return {
      url: url,
      title: data.metadata.title || '',
      description: data.metadata.description || '',
      image: data.metadata.image || '',
      siteName: data.metadata.siteName || '',
      favicon: getFaviconUrl(url)
    };
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. The website may be slow to respond.');
    }
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
};

/**
 * Fallback method using allorigins.win which is reliable for CORS
 */
const fetchWithAllOrigins = async (url: string): Promise<WebsiteMetadata> => {
  // Create a controller for timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 8000); // 8-second timeout
  
  try {
    // Create a proxy URL for fetching metadata to avoid CORS issues
    // Add timestamp parameter to prevent caching
    const encodedUrl = encodeURIComponent(url);
    const proxyUrl = `https://api.allorigins.win/get?url=${encodedUrl}&timestamp=${Date.now()}`;
    
    const response = await fetch(proxyUrl, {
      signal: controller.signal,
      mode: 'cors', // Explicitly set CORS mode
      headers: {
        'Accept': 'application/json'
      }
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error('Failed to fetch website data');
    }
    
    const data = await response.json();
    
    if (!data.contents) {
      throw new Error('No content received from proxy');
    }
    
    const htmlContent = data.contents;
    
    // Create a DOM parser to extract metadata
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // Extract metadata with a more streamlined approach
    const metadata: WebsiteMetadata = {
      url: url,
      favicon: getFaviconUrl(url)
    };
    
    // Extract title with priority for OpenGraph tags
    metadata.title = 
      doc.querySelector('meta[property="og:title"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:title"]')?.getAttribute('content') ||
      doc.querySelector('title')?.textContent || '';
    
    // Extract description with priority
    metadata.description = 
      doc.querySelector('meta[property="og:description"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:description"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    
    // Extract image with fallbacks
    metadata.image = 
      doc.querySelector('meta[property="og:image"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:image"]')?.getAttribute('content') || '';
    
    // Extract site name
    metadata.siteName = 
      doc.querySelector('meta[property="og:site_name"]')?.getAttribute('content') || '';
    
    // If we got at least a title, consider it a success
    if (metadata.title) {
      return metadata;
    }
    
    throw new Error('Could not extract metadata from the website');
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. The website may be slow to respond.');
    }
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
};

/**
 * Fallback method using cors-anywhere
 * This is used when the allorigins proxy fails
 */
const fetchWithCorsAnywhere = async (url: string): Promise<WebsiteMetadata> => {
  // Create a controller for timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 8000); // 8-second timeout
  
  try {
    // Use the public cors-anywhere instance as a fallback
    // Note: This has request limits but works as a secondary fallback
    const corsProxy = 'https://cors-anywhere.herokuapp.com/';
    const proxyUrl = `${corsProxy}${url}`;
    
    const response = await fetch(proxyUrl, {
      signal: controller.signal,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'text/html'
      }
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error('Failed to fetch website data');
    }
    
    const htmlContent = await response.text();
    
    // Create a DOM parser to extract metadata
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // Create basic metadata object with URL and favicon
    const metadata: WebsiteMetadata = {
      url: url,
      favicon: getFaviconUrl(url)
    };
    
    // Extract title
    metadata.title = 
      doc.querySelector('meta[property="og:title"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:title"]')?.getAttribute('content') ||
      doc.querySelector('title')?.textContent || '';
    
    // Extract description
    metadata.description = 
      doc.querySelector('meta[property="og:description"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:description"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    
    // Extract image
    metadata.image = 
      doc.querySelector('meta[property="og:image"]')?.getAttribute('content') ||
      doc.querySelector('meta[name="twitter:image"]')?.getAttribute('content') || '';
    
    // Extract site name
    metadata.siteName = 
      doc.querySelector('meta[property="og:site_name"]')?.getAttribute('content') || '';
    
    // Use domain name as site name if not found
    if (!metadata.siteName && url) {
      try {
        metadata.siteName = new URL(url).hostname.replace('www.', '');
      } catch (e) {
        // Ignore URL parsing errors
      }
    }
    
    return metadata;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. The website may be slow to respond.');
    }
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
};

// Extract data from website for company analysis
export const analyzeWebsiteWithAI = async (url: string, formData?: any) => {
  try {
    const response = await fetch('/api/website-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, formData, mode: 'full' })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to analyze website: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error analyzing website with AI:', error);
    throw error;
  }
};

// Comprehensive website analysis with more data extraction
export const analyzeWebsiteComprehensive = async (url: string, formData?: any) => {
  try {
    const response = await fetch('/api/analyze-website-comprehensive', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, formData })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to perform comprehensive analysis: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error in comprehensive website analysis:', error);
    throw error;
  }
};
