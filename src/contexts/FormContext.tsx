import React, { createContext, useContext, useState, useEffect } from 'react';
import { sql } from '../integrations/neon/client';
import { toast } from '../hooks/use-toast';
import { setupApiInterceptor } from '../services/apiService';
import { useAnalytics } from '../services/analytics';

export interface FormData {
  // Step 1: Basic Info + Qualifier
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  primaryChallenge: 'tracking' | 'strategy' | 'performance' | 'scaling' | 'technology' | 'other' | '';
  otherChallenge?: string;
  
  // Step 2: Context Questions
  marketingBudget: 'under_10k' | '10k_50k' | '50k_200k' | 'over_200k' | 'unknown' | '';
  marketingSetup: 'self' | 'internal' | 'agency' | 'combination' | 'none' | '';
  website: string;
  challengeDescription: string;
  
  // Step 3: Booking + Expectations
  sessionDate?: string;
  sessionGoals: string;
  specificFocus?: string;
  agreedToTerms?: boolean;
  understoodPreparation?: boolean;
  
  // Legacy fields (keep for compatibility)
  companyName: string;
  businessType: 'startup' | 'established_business' | 'agency' | 'freelancer' | 'other' | '';
  fundingStatus: 'bootstrapped' | 'funded' | '';
  businessStage: 'early_stage' | 'growth_stage' | 'established' | 'enterprise' | '';
  industry: string;
  otherIndustry?: string;
  runningAds: 'yes' | 'no' | '';
  adsEffective?: 'yes' | 'no' | 'not_sure' | '';
  adBudget: 'less_than_5k' | '5k_10k' | '10k_20k' | '20k_50k' | '50k_100k' | '100k_plus' | '';
  marketingManagement: 'in_house' | 'agency' | 'mix' | 'not_managed' | '';
  decisionMakers: string[];
  implementationTimeline: 'right_now' | '1_3_months' | 'exploring' | '';
  marketingAreas: string[];
  marketingChallenges: string;
  sessionOutcomes: string;
  materialsToShare: string[];
  otherMaterials?: string;
  additionalInfo: string;
  aiSummary?: string;
  
  // Comprehensive Website Analysis
  websiteAnalysis?: {
    company_profile?: any;
    business_model?: any;
    products_services?: any;
    marketing_insights?: any;
    digital_presence?: any;
    competitive_landscape?: any;
    pain_points_opportunities?: any;
    contact_readiness?: any;
  };
  executiveSummary?: string;
  additionalWebsiteData?: any;
}

interface FormContextType {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  validateCurrentStep: () => boolean;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  leadId: string | null;
  formStartTime: Date | null;
}

const initialFormData: FormData = {
  // Simplified Step 1
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  primaryChallenge: '',
  otherChallenge: '',
  
  // Simplified Step 2
  marketingBudget: '',
  marketingSetup: '',
  website: '',
  challengeDescription: '',
  
  // Simplified Step 3
  sessionDate: '',
  sessionGoals: '',
  specificFocus: '',
  agreedToTerms: false,
  understoodPreparation: false,
  
  // Legacy fields (for compatibility)
  companyName: '',
  businessType: '',
  fundingStatus: '',
  businessStage: '',
  industry: '',
  runningAds: '',
  adBudget: '',
  marketingManagement: '',
  decisionMakers: [],
  implementationTimeline: '',
  marketingAreas: [],
  marketingChallenges: '',
  sessionOutcomes: '',
  materialsToShare: [],
  additionalInfo: ''
};

const FormContext = createContext<FormContextType | undefined>(undefined);

// Type guard functions to ensure values match our expected types
const validateBusinessType = (value: string): FormData['businessType'] => {
  const validTypes: FormData['businessType'][] = ['startup', 'established_business', 'agency', 'freelancer', 'other', ''];
  return validTypes.includes(value as FormData['businessType']) 
    ? (value as FormData['businessType']) 
    : '';
};

const validateFundingStatus = (value: string): FormData['fundingStatus'] => {
  const validTypes: FormData['fundingStatus'][] = ['bootstrapped', 'funded', ''];
  return validTypes.includes(value as FormData['fundingStatus']) 
    ? (value as FormData['fundingStatus']) 
    : '';
};

const validateBusinessStage = (value: string): FormData['businessStage'] => {
  const validTypes: FormData['businessStage'][] = ['early_stage', 'growth_stage', 'established', 'enterprise', ''];
  return validTypes.includes(value as FormData['businessStage']) 
    ? (value as FormData['businessStage']) 
    : '';
};

const validateRunningAds = (value: string): FormData['runningAds'] => {
  const validTypes: FormData['runningAds'][] = ['yes', 'no', ''];
  return validTypes.includes(value as FormData['runningAds']) 
    ? (value as FormData['runningAds']) 
    : '';
};

const validateAdsEffective = (value: string): FormData['adsEffective'] => {
  const validTypes: FormData['adsEffective'][] = ['yes', 'no', 'not_sure', ''];
  return validTypes.includes(value as FormData['adsEffective']) 
    ? (value as FormData['adsEffective']) 
    : '';
};

const validateAdBudget = (value: string): FormData['adBudget'] => {
  const validTypes: FormData['adBudget'][] = ['less_than_5k', '5k_10k', '10k_20k', '20k_50k', '50k_100k', '100k_plus', ''];
  return validTypes.includes(value as FormData['adBudget']) 
    ? (value as FormData['adBudget']) 
    : '';
};

const validateMarketingManagement = (value: string): FormData['marketingManagement'] => {
  const validTypes: FormData['marketingManagement'][] = ['in_house', 'agency', 'mix', 'not_managed', ''];
  return validTypes.includes(value as FormData['marketingManagement']) 
    ? (value as FormData['marketingManagement']) 
    : '';
};

const validateImplementationTimeline = (value: string): FormData['implementationTimeline'] => {
  const validTypes: FormData['implementationTimeline'][] = ['right_now', '1_3_months', 'exploring', ''];
  return validTypes.includes(value as FormData['implementationTimeline']) 
    ? (value as FormData['implementationTimeline']) 
    : '';
};

export const FormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Load saved data from localStorage
  const loadSavedData = () => {
    const saved = localStorage.getItem('bookingFormData');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.error('Error loading saved form data:', e);
      }
    }
    return initialFormData;
  };

  const [formData, setFormData] = useState<FormData>(loadSavedData);
  const [currentStep, setCurrentStep] = useState(() => {
    const savedStep = localStorage.getItem('bookingFormStep');
    return savedStep ? parseInt(savedStep) : 1;
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [leadId, setLeadId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStartTime, setFormStartTime] = useState<Date | null>(null);
  const [stepStartTime, setStepStartTime] = useState<Date | null>(null);
  const analytics = useAnalytics();

  // Setup API interceptor on initial render
  useEffect(() => {
    setupApiInterceptor();
  }, []);

  // Track form start on initial render
  useEffect(() => {
    const startTime = new Date();
    setFormStartTime(startTime);
    setStepStartTime(startTime);

    analytics.track('booking_form_started', {
      source: document.referrer || 'direct',
    });
  }, []); // Remove analytics dependency to prevent re-tracking

  // Load lead data from localStorage on initial render
  useEffect(() => {
    const savedLeadId = localStorage.getItem('leadId');
    if (savedLeadId) {
      setLeadId(savedLeadId);
      fetchLeadData(savedLeadId);
    }
  }, []);

  // TODO: Migrate to Neon database
  // Fetch lead data from database when leadId is available
  const fetchLeadData = async (id: string) => {
    try {
      // TODO: Replace with Neon query
      console.log('TODO: Fetch lead data from Neon database for ID:', id);
      return;

      // const data = await sql`SELECT * FROM leads WHERE id = ${id}`;
      // if (!data || data.length === 0) {
      //   console.error('Lead not found');
      //   return;
      // }

      if (data) {
        // Populate form data from database
        setFormData({
          firstName: data.first_name || '',
          lastName: data.last_name || '',
          email: data.email || '',
          companyName: data.company_name || '',
          website: data.website || '',
          businessType: validateBusinessType(data.business_type || ''),
          fundingStatus: validateFundingStatus(data.funding_status || ''),
          businessStage: validateBusinessStage(data.business_stage || ''),
          industry: data.industry || '',
          otherIndustry: data.other_industry || '',
          runningAds: validateRunningAds(data.running_ads || ''),
          adsEffective: validateAdsEffective(data.ads_effective || ''),
          adBudget: validateAdBudget(data.ad_budget || ''),
          marketingManagement: validateMarketingManagement(data.marketing_management || ''),
          decisionMakers: data.decision_makers || [],
          implementationTimeline: validateImplementationTimeline(data.implementation_timeline || ''),
          marketingAreas: data.marketing_areas || [],
          marketingChallenges: data.marketing_challenges || '',
          sessionOutcomes: data.session_outcomes || '',
          materialsToShare: data.materials_to_share || [],
          otherMaterials: data.other_materials || '',
          additionalInfo: data.additional_info || '',
        });
        
        // Set the current step from database
        setCurrentStep(data.current_step || 1);
      }
    } catch (err) {
      console.error('Error fetching lead data:', err);
    }
  };

  // Save lead data to Supabase
  const saveLeadData = async (step: number) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      // Prepare data for Supabase
      const leadData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        company_name: formData.companyName,
        website: formData.website,
        business_type: formData.businessType,
        funding_status: formData.fundingStatus,
        business_stage: formData.businessStage,
        industry: formData.industry,
        other_industry: formData.otherIndustry,
        running_ads: formData.runningAds,
        ads_effective: formData.adsEffective,
        ad_budget: formData.adBudget,
        marketing_management: formData.marketingManagement,
        decision_makers: formData.decisionMakers,
        implementation_timeline: formData.implementationTimeline,
        marketing_areas: formData.marketingAreas,
        marketing_challenges: formData.marketingChallenges,
        session_outcomes: formData.sessionOutcomes,
        materials_to_share: formData.materialsToShare,
        other_materials: formData.otherMaterials,
        additional_info: formData.additionalInfo,
        current_step: step,
        is_completed: step === 5
      };

      let response;
      
      // TODO: Migrate to Neon database
      console.log('TODO: Save lead data to Neon database:', leadData);

      // if (leadId) {
      //   // Update existing lead
      //   await sql`UPDATE leads SET ... WHERE id = ${leadId}`;
      // } else {
      //   // Create new lead
      //   const result = await sql`INSERT INTO leads (...) VALUES (...) RETURNING id`;
      //   if (result[0]) {
      //     setLeadId(result[0].id);
      //     localStorage.setItem('leadId', result[0].id);
      //   }
      // }

      console.log('Successfully saved lead data for step', step);
    } catch (err) {
      console.error('Error saving lead data:', err);
      toast({
        title: "Fejl ved lagring af dine fremskridt",
        description: "Vi kunne ikke gemme dine oplysninger. Prøv venligst igen.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => {
      const updated = { ...prev, ...data };
      // Auto-save to localStorage
      localStorage.setItem('bookingFormData', JSON.stringify(updated));
      return updated;
    });
  };

  // Save current step to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('bookingFormStep', currentStep.toString());
  }, [currentStep]);

  const goToNextStep = () => {
    if (validateCurrentStep()) {
      const nextStep = currentStep + 1;

      // Track step completion
      const stepNames = ['Basic Information', 'Business Context', 'Marketing Details', 'Session Preparation', 'AI Summary Review'];
      analytics.track('booking_form_step_completed', {
        step: currentStep,
        step_name: stepNames[currentStep - 1] || `Step ${currentStep}`,
      });

      // Save data when moving to step 2 or beyond
      if (currentStep >= 1) {
        saveLeadData(nextStep);
      }

      setCurrentStep(nextStep);
      setStepStartTime(new Date());
      window.scrollTo(0, 0);
    } else {
      // Only track validation errors once per step attempt (not on every click)
      const errorKey = `step_${currentStep}_validation_error`;
      const lastErrorTime = sessionStorage.getItem(errorKey);
      const now = Date.now();
      
      if (!lastErrorTime || now - parseInt(lastErrorTime) > 5000) {
        sessionStorage.setItem(errorKey, now.toString());
        analytics.track('booking_form_error', {
          step: currentStep,
          error_type: 'validation_failed',
          error_count: Object.keys(errors).length,
        });
      }
    }
  };

  const goToPreviousStep = () => {
    // Only track step abandonment if user spent more than 2 seconds on the step
    const timeSpent = stepStartTime ? Math.floor((new Date().getTime() - stepStartTime.getTime()) / 1000) : 0;
    
    if (timeSpent > 2) {
      const stepNames = ['Basic Information', 'Business Context', 'Marketing Details', 'Session Preparation'];
      analytics.track('booking_form_step_abandoned', {
        step: currentStep,
        step_name: stepNames[currentStep - 1] || `Step ${currentStep}`,
        time_spent_seconds: timeSpent,
      });
    }

    setCurrentStep(prev => Math.max(1, prev - 1));
    setStepStartTime(new Date());
    window.scrollTo(0, 0);
  };

  // Validate current step
  const validateCurrentStep = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Step 1 validation - Check if using simplified form (primaryChallenge exists)
    if (currentStep === 1) {
      if (!formData.firstName.trim()) {
        newErrors.firstName = 'Fornavn er påkrævet';
        isValid = false;
      }
      
      if (!formData.lastName.trim()) {
        newErrors.lastName = 'Efternavn er påkrævet';
        isValid = false;
      }
      
      if (!formData.email.trim()) {
        newErrors.email = 'E-mail er påkrævet';
        isValid = false;
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Indtast venligst en gyldig e-mail';
        isValid = false;
      }
      
      // Company name is required for both old and new forms
      if (!formData.companyName.trim()) {
        newErrors.companyName = 'Virksomhedsnavn er påkrævet';
        isValid = false;
      }
      
      // Check if this is the simplified form by looking for primaryChallenge
      if ('primaryChallenge' in formData) {
        // Simplified form validation
        if (!formData.primaryChallenge) {
          newErrors.primaryChallenge = 'Vælg venligst din primære udfordring';
          isValid = false;
        }
      }
      
      if (!formData.website.trim()) {
        newErrors.website = 'Hjemmeside er påkrævet';
        isValid = false;
      } else {
        try {
          new URL(formData.website.startsWith('http') ? formData.website : `https://${formData.website}`);
        } catch (e) {
          newErrors.website = 'Indtast venligst en gyldig URL';
          isValid = false;
        }
      }
    }

    // Step 2 validation
    else if (currentStep === 2) {
      // Check if this is the simplified form
      if ('primaryChallenge' in formData) {
        // Simplified form validation
        if (!formData.marketingSetup) {
          newErrors.marketingSetup = 'Vælg venligst hvem der står for jeres marketing';
          isValid = false;
        }
        
        if (!formData.runningAds) {
          newErrors.runningAds = 'Vælg venligst om I kører annoncering';
          isValid = false;
        }
        
        // Only validate budget if they're running ads
        if (formData.runningAds === 'yes' && !formData.marketingBudget) {
          newErrors.marketingBudget = 'Vælg venligst jeres annoncebudget';
          isValid = false;
        }
        
        if (!formData.challengeDescription?.trim()) {
          newErrors.challengeDescription = 'Beskriv venligst jeres udfordring';
          isValid = false;
        }
      } else {
        // Old form validation
        if (!formData.businessType) {
          newErrors.businessType = 'Vælg venligst din virksomhedstype';
          isValid = false;
        }
        
        // Only validate funding status for startups
        if (formData.businessType === 'startup' && !formData.fundingStatus) {
          newErrors.fundingStatus = 'Vælg venligst din finansieringsstatus';
          isValid = false;
        }
        
        if (formData.businessType === 'startup' && !formData.businessStage) {
          newErrors.businessStage = 'Vælg venligst dit virksomhedsstadie';
          isValid = false;
        }
        
        if (!formData.industry) {
          newErrors.industry = 'Vælg eller indtast din branche';
          isValid = false;
        }
        
        if (formData.industry === 'other' && !formData.otherIndustry?.trim()) {
          newErrors.otherIndustry = 'Angiv venligst din branche';
          isValid = false;
        }
      }
    }

    // Step 3 validation
    else if (currentStep === 3) {
      if (!formData.runningAds) {
        newErrors.runningAds = 'Vælg venligst en mulighed';
        isValid = false;
      }
      
      if (formData.runningAds === 'yes' && !formData.adsEffective) {
        newErrors.adsEffective = 'Vælg venligst en mulighed';
        isValid = false;
      }
      
      if (formData.runningAds === 'yes' && !formData.adBudget) {
        newErrors.adBudget = 'Vælg venligst dit annoncebudget';
        isValid = false;
      }
      
      if (!formData.marketingManagement) {
        newErrors.marketingManagement = 'Vælg venligst en mulighed';
        isValid = false;
      }
      
      if (formData.decisionMakers.length === 0) {
        newErrors.decisionMakers = 'Vælg venligst mindst én mulighed';
        isValid = false;
      }
      
      if (!formData.implementationTimeline) {
        newErrors.implementationTimeline = 'Vælg venligst en tidslinje';
        isValid = false;
      }
      
      if (formData.marketingAreas.length === 0) {
        newErrors.marketingAreas = 'Vælg venligst mindst ét område';
        isValid = false;
      }
    }

    // Step 4 validation - minimal validation as these are more open-ended
    else if (currentStep === 4) {
      if (!formData.marketingChallenges.trim()) {
        newErrors.marketingChallenges = 'Del venligst dine marketing udfordringer';
        isValid = false;
      }
      
      if (!formData.sessionOutcomes.trim()) {
        newErrors.sessionOutcomes = 'Del venligst dine ønskede resultater';
        isValid = false;
      }
    }
    
    // Step 5 validation - ensure AI summary exists
    else if (currentStep === 5) {
      if (!formData.aiSummary || !formData.aiSummary.trim()) {
        newErrors.aiSummary = 'AI sammendrag er påkrævet';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  return (
    <FormContext.Provider
      value={{
        formData,
        updateFormData,
        currentStep,
        setCurrentStep,
        goToNextStep,
        goToPreviousStep,
        validateCurrentStep,
        errors,
        setErrors,
        leadId,
        formStartTime
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
};
