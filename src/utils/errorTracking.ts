import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

// Initialize Sentry for error tracking
export const initErrorTracking = () => {
  const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
  
  if (!sentryDsn) {
    console.warn('Sentry DSN not provided. Error tracking disabled.');
    return;
  }

  Sentry.init({
    dsn: sentryDsn,
    environment: import.meta.env.MODE,
    integrations: [
      new BrowserTracing({
        // Set sampling rate for performance monitoring
        tracePropagationTargets: ['localhost', 'asger.me', /^\//],
      }),
    ],
    
    // Performance monitoring
    tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0,
    
    // Error sampling
    sampleRate: 1.0,
    
    // Release tracking
    release: import.meta.env.VITE_APP_VERSION || '1.0.0',
    
    // Additional configuration
    beforeSend(event, hint) {
      // Filter out non-critical errors in production
      if (import.meta.env.PROD) {
        const error = hint.originalException;
        
        // Skip network errors
        if (error && error.toString().includes('NetworkError')) {
          return null;
        }
        
        // Skip script loading errors from extensions
        if (event.exception?.values?.[0]?.value?.includes('extension')) {
          return null;
        }
      }
      
      return event;
    },
    
    // Set user context
    initialScope: {
      tags: {
        component: 'asger-website',
      },
      user: {
        id: 'anonymous',
      },
    },
  });
};

// Custom error boundary component
export const ErrorBoundary = Sentry.withErrorBoundary;

// Manual error reporting
export const reportError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('additional_info', context);
    }
    Sentry.captureException(error);
  });
};

// Performance monitoring
export const startTransaction = (name: string, op: string) => {
  // startTransaction is deprecated in newer Sentry versions
  // Use startSpan instead for performance monitoring
  return null; // Disabled for now
};

// User feedback
export const showReportDialog = () => {
  Sentry.showReportDialog();
};

// Set user context
export const setUser = (user: { id: string; email?: string; username?: string }) => {
  Sentry.setUser(user);
};

// Add breadcrumb
export const addBreadcrumb = (message: string, category: string, level: string = 'info') => {
  Sentry.addBreadcrumb({
    message,
    category,
    level: level as Sentry.SeverityLevel,
    timestamp: Date.now() / 1000,
  });
};
