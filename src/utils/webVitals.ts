import { onCLS, onFCP, onLCP, onTTFB, onINP } from 'web-vitals';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

// Function to send metrics to analytics
const sendToAnalytics = (metric: WebVitalMetric) => {
  // Disable PostHog web vitals tracking to reduce event volume
  // Only log in development for debugging
  if (import.meta.env.DEV) {
    console.log('Web Vital:', metric);
  }

  // Send to Google Analytics if available (less rate limiting)
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }
};

// Initialize web vitals monitoring
export const initWebVitals = () => {
  // Core Web Vitals
  onCLS(sendToAnalytics);
  onLCP(sendToAnalytics);

  // Other important metrics
  onFCP(sendToAnalytics);
  onTTFB(sendToAnalytics);

  // New metric (replaces FID in Chrome 96+)
  onINP(sendToAnalytics);
};

// Performance observer for additional metrics
export const initPerformanceObserver = () => {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  // Monitor long tasks
  try {
    const longTaskObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) {
          sendToAnalytics({
            name: 'long-task',
            value: entry.duration,
            rating: entry.duration > 100 ? 'poor' : 'needs-improvement',
            delta: entry.duration,
            id: `long-task-${Date.now()}`,
          });
        }
      });
    });
    longTaskObserver.observe({ entryTypes: ['longtask'] });
  } catch (e) {
    // Long task API not supported
  }

  // Monitor layout shifts
  try {
    const layoutShiftObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      list.getEntries().forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      
      if (clsValue > 0) {
        sendToAnalytics({
          name: 'layout-shift',
          value: clsValue,
          rating: clsValue > 0.25 ? 'poor' : clsValue > 0.1 ? 'needs-improvement' : 'good',
          delta: clsValue,
          id: `layout-shift-${Date.now()}`,
        });
      }
    });
    layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
  } catch (e) {
    // Layout shift API not supported
  }
};

// Declare global types for analytics
declare global {
  interface Window {
    posthog?: any;
    gtag?: (...args: any[]) => void;
  }
}
