import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Globe, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { analyzeWebsiteComprehensive } from './services/websiteMetadataService';

const TestRealAPI = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const testWebsites = [
    { name: 'QuickOrder', url: 'https://quickorder.io/dk/' },
    { name: 'Zendesk', url: 'https://zendesk.com' },
    { name: 'Shopify', url: 'https://shopify.com' },
    { name: 'Stripe', url: 'https://stripe.com' },
    { name: 'Anthropic', url: 'https://anthropic.com' },
    { name: 'OpenAI', url: 'https://openai.com' },
  ];

  const analyzeWebsite = async () => {
    if (!url) {
      toast.error('Please enter a website URL');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      // Normalize URL
      const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
      
      toast.info('Starting comprehensive website analysis...');
      
      const response = await analyzeWebsiteComprehensive(normalizedUrl, {
        companyName: 'Test Company',
        industry: 'Technology'
      });

      if (response.success) {
        setResult(response);
        toast.success('Analysis completed successfully!');
      } else {
        throw new Error(response.error || 'Analysis failed');
      }
    } catch (err: any) {
      console.error('Analysis error:', err);
      const errorMessage = err.message || 'Failed to analyze website';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const checkAPIHealth = async () => {
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      
      if (data.status === 'ok') {
        toast.success('API is healthy!', {
          description: `OpenAI: ${data.env.hasOpenAI ? '✅' : '❌'}, Firecrawl: ${data.env.hasFirecrawl ? '✅' : '❌'}`
        });
      }
    } catch (err) {
      toast.error('API health check failed');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Test Real API Integration</h1>
          <p className="text-muted-foreground">
            Test the actual Firecrawl and OpenAI API endpoints
          </p>
        </div>

        {/* API Health Check */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              API Configuration
              <Button variant="outline" size="sm" onClick={checkAPIHealth}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Health
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Setup Instructions</AlertTitle>
              <AlertDescription>
                <ol className="list-decimal list-inside space-y-1 mt-2 text-sm">
                  <li>Make sure your <code>.env</code> file contains valid API keys</li>
                  <li>Run <code>npm run dev:real</code> to start both servers</li>
                  <li>The API server will run on <code>http://localhost:3000</code></li>
                  <li>Check the terminal for API key configuration status</li>
                </ol>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* URL Input */}
        <Card>
          <CardHeader>
            <CardTitle>Analyze Website</CardTitle>
            <CardDescription>
              Enter any website URL to test the comprehensive analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                type="url"
                placeholder="Enter website URL (e.g., quickorder.io)"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && analyzeWebsite()}
                className="flex-1"
              />
              <Button onClick={analyzeWebsite} disabled={loading || !url}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Globe className="mr-2 h-4 w-4" />
                    Analyze
                  </>
                )}
              </Button>
            </div>

            {/* Quick test buttons */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-muted-foreground">Quick test:</span>
              {testWebsites.map((site) => (
                <Button
                  key={site.url}
                  variant="outline"
                  size="sm"
                  onClick={() => setUrl(site.url)}
                >
                  {site.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Results */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Analysis Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="summary" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                  <TabsTrigger value="analysis">Analysis</TabsTrigger>
                  <TabsTrigger value="additional">Additional</TabsTrigger>
                  <TabsTrigger value="raw">Raw JSON</TabsTrigger>
                </TabsList>

                <TabsContent value="summary" className="space-y-4 mt-4">
                  <div>
                    <h3 className="font-semibold mb-2">Executive Summary (Danish)</h3>
                    <div className="prose prose-sm max-w-none bg-muted p-4 rounded-lg">
                      <p className="whitespace-pre-wrap">{result.executiveSummary}</p>
                    </div>
                  </div>
                  
                  {result.metadata && (
                    <div>
                      <h3 className="font-semibold mb-2">Metadata</h3>
                      <dl className="grid grid-cols-2 gap-2 text-sm">
                        <dt className="font-medium">Title:</dt>
                        <dd>{result.metadata.title}</dd>
                        <dt className="font-medium">Description:</dt>
                        <dd>{result.metadata.description}</dd>
                        <dt className="font-medium">Analyzed at:</dt>
                        <dd>{new Date(result.metadata.analyzedAt).toLocaleString()}</dd>
                      </dl>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="analysis" className="mt-4">
                  <div className="space-y-4">
                    {result.analysis && Object.entries(result.analysis).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-sm mb-1 capitalize">
                          {key.replace(/_/g, ' ')}
                        </h4>
                        <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-48">
                          {JSON.stringify(value, null, 2)}
                        </pre>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="additional" className="mt-4">
                  {result.additionalData && (
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                      {JSON.stringify(result.additionalData, null, 2)}
                    </pre>
                  )}
                </TabsContent>

                <TabsContent value="raw" className="mt-4">
                  <pre className="text-xs bg-muted p-4 rounded overflow-auto max-h-96">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default TestRealAPI;