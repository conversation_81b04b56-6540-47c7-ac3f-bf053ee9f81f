import React, { Component, ReactNode } from 'react';
import { PostHogProvider } from 'posthog-js/react';
import App from '../App';

interface AppWithAnalyticsProps {
  posthogKey: string | null;
  options: {
    api_host: string;
    person_profiles: 'identified_only';
    disabled: boolean;
    capture_pageview: boolean;
    capture_pageleave: boolean;
    loaded: (posthog: unknown) => void;
  };
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class PostHogErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('PostHog initialization error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // If PostHog fails, just render the app without analytics
      return <App />;
    }

    return this.props.children;
  }
}

const AppWithAnalytics: React.FC<AppWithAnalyticsProps> = ({ posthogKey, options }) => {
  // Only render PostHogProvider if we have a valid API key
  if (!posthogKey || options.disabled) {
    return <App />;
  }

  return (
    <PostHogErrorBoundary>
      <PostHogProvider apiKey={posthogKey} options={options}>
        <App />
      </PostHogProvider>
    </PostHogErrorBoundary>
  );
};

export default AppWithAnalytics;
