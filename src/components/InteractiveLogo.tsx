import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, User, Calendar, MessageCircle, X, Mail, HelpCircle, Target, Sparkles } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from './ui/dropdown-menu';

interface InteractiveLogoProps {
  className?: string;
  variant?: 'default' | 'compact';
  showStatusText?: boolean;
  isScrolled?: boolean;
}

type OnlineStatus = 'online' | 'away' | 'offline';

const InteractiveLogo: React.FC<InteractiveLogoProps> = ({
  className = '',
  variant = 'default',
  showStatusText = true,
  isScrolled = false,
}) => {
  const navigate = useNavigate();
  const [onlineStatus, setOnlineStatus] = useState<OnlineStatus>('offline');
  const [isOpen, setIsOpen] = useState(false);
  const [hasHover, setHasHover] = useState(false);
  const hoverTimeoutRef = React.useRef<NodeJS.Timeout>();

  // Detect if device supports hover
  React.useEffect(() => {
    const checkHover = () => {
      setHasHover(window.matchMedia('(hover: hover)').matches);
    };
    checkHover();
    window.addEventListener('resize', checkHover);
    return () => {
      window.removeEventListener('resize', checkHover);
      clearTimeout(hoverTimeoutRef.current);
    };
  }, []);

  // Calculate online status based on current time
  useEffect(() => {
    const updateOnlineStatus = () => {
      const now = new Date();
      const hour = now.getHours();

      if (hour >= 8 && hour < 17) {
        setOnlineStatus('online');
      } else if ((hour >= 6 && hour < 8) || (hour >= 17 && hour < 22)) {
        setOnlineStatus('away');
      } else {
        setOnlineStatus('offline');
      }
    };

    // Update immediately
    updateOnlineStatus();

    // Update every minute
    const interval = setInterval(updateOnlineStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusStyle = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return { backgroundColor: '#10b981' }; // green-500
      case 'away':
        return { backgroundColor: '#eab308' }; // yellow-500
      case 'offline':
        return { backgroundColor: '#ef4444' }; // red-500
      default:
        return { backgroundColor: '#6b7280' }; // gray-500
    }
  };

  const getStatusText = (status: OnlineStatus) => {
    switch (status) {
      case 'online':
        return 'Klar til at hjælpe! ☕';
      case 'away':
        return 'Tjekker mails (måske sejler)';
      case 'offline':
        return 'Sover/familie-tid 😴';
      default:
        return 'Status unknown';
    }
  };

  const getTimeBasedMessage = () => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 8) return 'Første kaffe ☕';
    if (hour >= 8 && hour < 17) return 'Full speed ahead! 🚀';
    if (hour >= 17 && hour < 22) return 'Måske ledig 📧';
    return 'ZzZz... 😴';
  };

  const handleMouseEnter = () => {
    if (hasHover) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(true);
      }, 200);
    }
  };

  const handleMouseLeave = () => {
    if (hasHover) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
      }, 300);
    }
  };

  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate('/');
    window.scrollTo({ top: 0, behavior: 'smooth' });
    if (!hasHover) setIsOpen(false);
  };

  const handleDropdownClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* Mobile backdrop */}
      {!hasHover && isOpen && (
        <div 
          className="fixed inset-0 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
      
      <div 
        className="relative"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen} modal={false}>
        <DropdownMenuTrigger asChild>
          <button
            className={`group relative flex items-center gap-3 bg-white border-4 border-black rounded-full pl-1 pr-6 py-1 transition-all duration-300 ease-out hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] focus:outline-none ${
              isScrolled ? 'scale-90' : ''
            } ${className}`}
          >
            {/* Logo Section - Clickable for Home */}
            <div
              onClick={handleLogoClick}
              className="flex items-center gap-3 cursor-pointer"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleLogoClick(e as any);
                }
              }}
            >
              {/* Avatar with Status Indicator */}
              <div className="relative">
                <div className={`rounded-full overflow-hidden border-2 border-black bg-gray-100 transition-all duration-300 ${
                    isScrolled ? 'w-10 h-10' : 'w-12 h-12'
                  }`}>
                  <img
                    src="/me.png"
                    alt="Asger Profile"
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Status Indicator */}
                <div
                  className={`absolute -bottom-1 -left-1 w-4 h-4 rounded-full border-2 border-white transition-colors duration-300 ${getStatusColor(onlineStatus)}`}
                  title={getStatusText(onlineStatus)}
                  style={{
                    boxShadow: '0 0 0 2px black',
                    ...getStatusStyle(onlineStatus)
                  }}
                />
              </div>

              {/* Logo Text */}
              <span className={`font-bold tracking-tight text-black transition-all duration-300 ${
                  isScrolled ? 'text-base' : 'text-lg'
                }`}>
                Asger.me
              </span>
            </div>

            {/* Dropdown Trigger */}
            <div
              onClick={handleDropdownClick}
              className="cursor-pointer p-1 -m-1 rounded-full hover:bg-gray-100 transition-colors"
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleDropdownClick(e as any);
                }
              }}
            >
              <ChevronDown
                className={`w-4 h-4 text-black transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
              />
            </div>
          </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        className="w-64 neo-border neo-shadow bg-white p-0 rounded-lg overflow-hidden [&[data-state=open]]:animate-none [&[data-state=closed]]:animate-none z-50"
        sideOffset={8}
        align="start"
        avoidCollisions={true}
        side="bottom"
      >
        {/* Header Section */}
        <div className="p-4 bg-primary border-b-2 border-black relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-black/10 rounded-full translate-y-8 -translate-x-8"></div>

          <div className="flex items-center space-x-3 relative z-10">
            <div className="relative z-10">
              <div className="w-12 h-12 rounded-full overflow-hidden neo-border bg-gray-100">
                <img
                  src="/me.png"
                  alt="Asger Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              <div
                className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 rounded-full border-2 border-white transition-all duration-300 z-20 ${getStatusColor(onlineStatus)}`}
                style={{
                  boxShadow: '0 0 0 2px black',
                  ...getStatusStyle(onlineStatus)
                }}
              />
            </div>
            <div>
              <h3 className="font-bold text-black">Asger Teglgaard</h3>
              <p className="text-sm text-black/70">{getStatusText(onlineStatus)}</p>
            </div>
          </div>
        </div>

        {/* Navigation Items */}
        <div className="p-2">
          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              const element = document.getElementById('problem');
              element?.scrollIntoView({ behavior: 'smooth' });
              if (!hasHover) setIsOpen(false);
            }}
          >
            <Target className="w-4 h-4" />
            <span>Genkender du det?</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] rounded cursor-pointer transition-all duration-200 bg-primary/10 border-2 border-black hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-2px] hover:translate-y-[-2px] group"
            onClick={() => {
              navigate('/booking');
              if (!hasHover) setIsOpen(false);
            }}
          >
            <Calendar className="w-4 h-4 text-black" />
            <span className="text-black font-bold">Book 2 timer (5.750 kr)</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-accent/10 rounded cursor-pointer transition-colors border-2 border-transparent hover:border-accent"
            onClick={() => {
              navigate('/booking/intro');
              if (!hasHover) setIsOpen(false);
            }}
          >
            <Sparkles className="w-4 h-4 text-accent" />
            <span className="font-semibold">Gratis 10 min intro</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator className="border-gray-200 my-2" />

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              const element = document.getElementById('solution');
              element?.scrollIntoView({ behavior: 'smooth' });
              if (!hasHover) setIsOpen(false);
            }}
          >
            <Sparkles className="w-4 h-4" />
            <span>Sådan hjælper jeg</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              const element = document.getElementById('testimonials');
              element?.scrollIntoView({ behavior: 'smooth' });
              if (!hasHover) setIsOpen(false);
            }}
          >
            <MessageCircle className="w-4 h-4" />
            <span>Hvad folk siger</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              const element = document.getElementById('faq');
              element?.scrollIntoView({ behavior: 'smooth' });
              if (!hasHover) setIsOpen(false);
            }}
          >
            <HelpCircle className="w-4 h-4" />
            <span>FAQ</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator className="border-gray-200 my-2" />

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              navigate('/about');
              if (!hasHover) setIsOpen(false);
            }}
          >
            <User className="w-4 h-4" />
            <span>Om mig</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center space-x-3 p-3 min-h-[44px] hover:bg-primary/10 rounded cursor-pointer transition-colors"
            onClick={() => {
              navigate('/contact');
              if (!hasHover) setIsOpen(false);
            }}
          >
            <Mail className="w-4 h-4" />
            <span>Kontakt</span>
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator className="border-black" />


        {/* Mobile Close Button */}
        {!hasHover && (
          <div className="p-3 border-t-2 border-black">
            <button
              onClick={() => setIsOpen(false)}
              className="w-full neo-button bg-white hover:bg-gray-100 text-black py-2 flex items-center justify-center gap-2"
            >
              <X className="w-4 h-4" />
              <span>Luk menu</span>
            </button>
          </div>
        )}

        {/* Status Footer */}
        <div className="p-3 bg-gray-50 border-t-2 border-black">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${getStatusColor(onlineStatus)}`}
                style={getStatusStyle(onlineStatus)}
              />
              <span className="text-xs text-gray-600">
                {onlineStatus === 'online' ? 'Available now' : 
                 onlineStatus === 'away' ? 'Back soon' : 'Offline'}
              </span>
            </div>
            <span className="text-xs text-gray-500">
              {getTimeBasedMessage()}
            </span>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
    </div>
    </>
  );
};

export default InteractiveLogo;