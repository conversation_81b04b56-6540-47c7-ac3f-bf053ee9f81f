import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';

interface LanguageSwitcherProps {
  variant?: 'header' | 'footer' | 'standalone';
  className?: string;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  variant = 'header',
  className = ''
}) => {
  const { i18n, t } = useTranslation('common');

  const toggleLanguage = () => {
    const newLang = i18n.language === 'da' ? 'en' : 'da';
    i18n.changeLanguage(newLang);
    
    // Update document language for SEO
    document.documentElement.lang = newLang;
    
    // Update meta description if available
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      // This would be handled by the SEO component in a real implementation
      console.log('Language changed to:', newLang);
    }
  };

  const currentLanguage = i18n.language === 'da' ? 'danish' : 'english';
  const targetLanguage = i18n.language === 'da' ? 'english' : 'danish';

  if (variant === 'header') {
    return (
      <button
        onClick={toggleLanguage}
        className={`neo-button bg-white hover:bg-muted text-black text-sm flex items-center gap-2 ${className}`}
        aria-label={t('language.switchTo', { language: t(`language.${targetLanguage}`) })}
        title={t('language.switchTo', { language: t(`language.${targetLanguage}`) })}
      >
        <Globe size={16} />
        <span className="font-bold">
          {i18n.language === 'da' ? 'EN' : 'DA'}
        </span>
      </button>
    );
  }

  if (variant === 'footer') {
    return (
      <button
        onClick={toggleLanguage}
        className={`text-sm text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-1 ${className}`}
        aria-label={t('language.switchTo', { language: t(`language.${targetLanguage}`) })}
      >
        <Globe size={14} />
        <span>{i18n.language === 'da' ? 'English' : 'Dansk'}</span>
      </button>
    );
  }

  // Standalone variant
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Globe size={20} className="text-gray-600" />
      <select
        value={i18n.language}
        onChange={(e) => {
          i18n.changeLanguage(e.target.value);
          document.documentElement.lang = e.target.value;
        }}
        className="neo-card p-2 bg-white border-2 border-black text-sm font-bold"
        aria-label={t('language.switchTo', { language: '' })}
      >
        <option value="da">{t('language.danish')}</option>
        <option value="en">{t('language.english')}</option>
      </select>
    </div>
  );
};

export default LanguageSwitcher;
