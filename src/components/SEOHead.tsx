import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  noindex?: boolean;
  structuredData?: object;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  canonical,
  ogImage = '/og-image.jpg',
  ogType = 'website',
  noindex = false,
  structuredData
}) => {
  const { i18n } = useTranslation();
  const currentLang = i18n.language;
  const alternateLang = currentLang === 'da' ? 'en' : 'da';
  
  // Get current URL without language prefix for hreflang
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const baseUrl = 'https://asger.me';
  
  // Generate alternate URLs for hreflang
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
  const alternateUrl = `${baseUrl}${currentPath}`;
  
  // Default meta content based on language
  const defaultTitle = currentLang === 'da' 
    ? 'Asger.me - Marketing Expert & Strategisk Konsulent'
    : 'Asger.me - Marketing Expert & Strategic Consultant';
    
  const defaultDescription = currentLang === 'da'
    ? 'Marketing terapi og strategisk rådgivning fra Asger Teglgaard. 10+ års erfaring med growth, B2B SaaS og performance marketing. Ingen BS - bare marketing der virker.'
    : 'Marketing therapy and strategic consulting from Asger Teglgaard. 10+ years experience with growth, B2B SaaS and performance marketing. No BS - just marketing that works.';

  const defaultKeywords = currentLang === 'da'
    ? 'marketing konsulent, strategisk rådgivning, growth marketing, B2B SaaS, performance marketing, marketing terapi, Asger Teglgaard, Danmark'
    : 'marketing consultant, strategic consulting, growth marketing, B2B SaaS, performance marketing, marketing therapy, Asger Teglgaard, Denmark';

  const finalTitle = title || defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalKeywords = keywords || defaultKeywords;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <html lang={currentLang} />
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content="Asger Teglgaard" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#10b981" />
      <meta name="color-scheme" content="light" />

      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Hreflang for SEO */}
      <link rel="alternate" hrefLang="da" href={`${baseUrl}/da${currentPath}`} />
      <link rel="alternate" hrefLang="en" href={`${baseUrl}/en${currentPath}`} />
      <link rel="alternate" hrefLang="x-default" href={`${baseUrl}${currentPath}`} />
      
      {/* Open Graph */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={`${baseUrl}${ogImage}`} />
      <meta property="og:locale" content={currentLang === 'da' ? 'da_DK' : 'en_US'} />
      <meta property="og:locale:alternate" content={currentLang === 'da' ? 'en_US' : 'da_DK'} />
      <meta property="og:site_name" content="Asger.me" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={`${baseUrl}${ogImage}`} />
      <meta name="twitter:creator" content="@asgerteglgaard" />
      
      {/* Robots */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      )}

      {/* Additional SEO Meta Tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Asger.me" />

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://eu.i.posthog.com" />

      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://eu.i.posthog.com" />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
      
      {/* Language-specific structured data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Person",
          "name": "Asger Teglgaard",
          "url": "https://asger.me",
          "jobTitle": currentLang === 'da' ? "Marketing Konsulent" : "Marketing Consultant",
          "description": finalDescription,
          "knowsAbout": [
            "Marketing Strategy",
            "Growth Marketing", 
            "B2B SaaS",
            "Performance Marketing",
            "AI/ChatGPT Tools"
          ],
          "sameAs": [
            "https://linkedin.com/in/asgerteglgaard",
            "https://twitter.com/asgerteglgaard"
          ],
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "DK",
            "addressLocality": "Denmark"
          },
          "email": "<EMAIL>",
          "telephone": "+45 93 93 77 96"
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
