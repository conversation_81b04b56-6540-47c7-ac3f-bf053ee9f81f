import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, Zap, Clock, ThumbsUp, Calendar } from 'lucide-react';
import { useAnalytics } from '../services/analytics';

interface SolutionSectionProps {
  onBookingClick?: () => void;
}

const SolutionSection: React.FC<SolutionSectionProps> = ({ onBookingClick }) => {
  const { t } = useTranslation('home');
  const navigate = useNavigate();
  const analytics = useAnalytics();

  const handleBookingClick = () => {
    analytics.track('cta_clicked', {
      cta_text: 'Book 2 timer',
      cta_location: 'solution_section',
      destination: '/booking',
    });
    
    if (onBookingClick) {
      onBookingClick();
    } else {
      navigate('/booking');
    }
  };

  const benefits = [
    { icon: <CheckCircle className="w-5 h-5" />, text: t('solution.benefits.review') },
    { icon: <CheckCircle className="w-5 h-5" />, text: t('solution.benefits.actions') },
    { icon: <CheckCircle className="w-5 h-5" />, text: t('solution.benefits.tools') },
    { icon: <CheckCircle className="w-5 h-5" />, text: t('solution.benefits.followup') }
  ];

  return (
    <section id="solution" className="py-32 bg-white relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-20 right-10 w-24 h-24 bg-primary neo-border rotate-12 opacity-20" />
      <div className="absolute bottom-20 left-10 w-32 h-32 bg-accent neo-border -rotate-6 opacity-20" />
      
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-primary text-black font-bold mb-6 rotate-1">
            {t('solution.badge')}
          </span>
          <h2 className="text-4xl md:text-6xl font-bold mb-4">
            {t('solution.title')}
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="neo-card p-8 md:p-12 bg-white -rotate-1 hover:rotate-0 transition-transform duration-300">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left side - What you get */}
              <div>
                <h3 className="text-2xl font-bold mb-6">{t('solution.whatYouGet')}</h3>
                <div className="space-y-4 mb-8">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <span className="text-primary mt-1">{benefit.icon}</span>
                      <span className="text-gray-700">{benefit.text}</span>
                    </div>
                  ))}
                </div>

                <div className="flex items-center gap-6 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>60 min</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    <span>{t('solution.noBinding')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ThumbsUp className="w-4 h-4" />
                    <span>{t('solution.noBS')}</span>
                  </div>
                </div>
              </div>

              {/* Right side - Pricing */}
              <div className="bg-muted p-6 neo-border">
                <div className="text-center mb-6">
                  <p className="text-lg font-bold mb-2">Start med gratis diagnose</p>
                  <p className="text-sm text-gray-600">Så ved du om jeg kan hjælpe</p>
                </div>

                <button
                  onClick={() => {
                    analytics.track('cta_clicked', {
                      cta_text: t('solution.cta.primary'),
                      cta_location: 'solution_section',
                      destination: '/booking/intro',
                    });
                    navigate('/booking/intro');
                  }}
                  className="w-full neo-button bg-primary hover:bg-primary/90 text-black text-lg py-4 mb-4 group animate-pulse"
                >
                  <span className="flex items-center justify-center gap-2">
                    <Calendar className="w-5 h-5 group-hover:animate-bounce-light" />
                    {t('solution.cta.primary')}
                  </span>
                </button>

                <div className="text-center mb-4">
                  <span className="text-sm text-gray-600">eller</span>
                </div>

                <button
                  onClick={handleBookingClick}
                  className="w-full neo-button bg-white hover:bg-gray-50 text-black py-3 mb-4"
                >
                  <span className="text-sm">
                    {t('solution.cta.secondary')}
                  </span>
                </button>

                <div className="text-center">
                  <p className="text-red-600 font-semibold text-sm animate-pulse">
                    {t('solution.nextAvailable')}
                  </p>
                </div>
              </div>
            </div>

            {/* Bottom testimonial */}
            <div className="mt-8 pt-8 border-t-2 border-black">
              <blockquote className="text-center">
                <p className="text-lg italic mb-2">
                  "{t('solution.testimonial.quote')}"
                </p>
                <cite className="text-sm text-gray-600 not-italic">
                  — {t('solution.testimonial.author')}
                </cite>
              </blockquote>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;