import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ArrowR<PERSON>, Anchor, Code, Heart } from 'lucide-react';

const AboutMini = () => {
  const { t } = useTranslation('home');
  const navigate = useNavigate();

  return (
    <section id="about" className="py-32 bg-muted relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 right-10 w-20 h-20 bg-primary neo-border rotate-45 opacity-20" />
      <div className="absolute bottom-10 left-10 w-16 h-16 bg-accent neo-border -rotate-12 opacity-20" />
      
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Image side */}
            <div className="relative">
              <div className="neo-card overflow-hidden bg-white p-4 rotate-2">
                <img 
                  src="/me.png" 
                  alt="Asger <PERSON>gl<PERSON>" 
                  className="w-full h-auto rounded"
                />
              </div>
              {/* Fun badges */}
              <div className="absolute -top-4 -left-4 px-3 py-1 neo-border bg-primary text-black text-sm font-bold rotate-12">
                10+ år
              </div>
              <div className="absolute -bottom-4 -right-4 px-3 py-1 neo-border bg-accent text-white text-sm font-bold -rotate-6">
                AI nørd
              </div>
            </div>

            {/* Content side */}
            <div>
              <span className="inline-block px-4 py-2 neo-border bg-white text-black font-bold mb-6 -rotate-1">
                {t('aboutMini.badge')}
              </span>
              
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {t('aboutMini.title')}
              </h2>
              
              <div className="space-y-4 text-gray-700">
                <p>{t('aboutMini.intro')}</p>
                
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex items-center gap-3">
                    <Code className="w-5 h-5 text-primary" />
                    <span>{t('aboutMini.points.experience')}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Heart className="w-5 h-5 text-accent" />
                    <span>{t('aboutMini.points.ai')}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Anchor className="w-5 h-5 text-blue" />
                    <span>{t('aboutMini.points.personal')}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={() => navigate('/about')}
                className="mt-8 neo-button bg-white hover:bg-muted inline-flex items-center gap-2 group"
              >
                <span>{t('aboutMini.cta')}</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutMini;