import React, { useState, useEffect } from 'react';
import { X, Mail, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalytics } from '../services/analytics';
import { useToast } from '../hooks/use-toast';

const NewsletterSlideIn: React.FC = () => {
  const { t } = useTranslation('common');
  const analytics = useAnalytics();
  const { toast } = useToast();
  
  const [isVisible, setIsVisible] = useState(false);
  const [secondsElapsed, setSecondsElapsed] = useState(0);
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Check if user has already seen the popup
    const hasSeenPopup = localStorage.getItem('newsletter-popup-seen');
    const lastShown = localStorage.getItem('newsletter-popup-last-shown');
    
    // Don't show if already subscribed
    const isSubscribed = localStorage.getItem('newsletter-subscribed');
    if (isSubscribed === 'true') return;
    
    // Don't show if seen in last 7 days
    if (hasSeenPopup && lastShown) {
      const daysSinceLastShown = (Date.now() - parseInt(lastShown)) / (1000 * 60 * 60 * 24);
      if (daysSinceLastShown < 7) return;
    }

    // Start timer
    const interval = setInterval(() => {
      setSecondsElapsed(prev => {
        const newSeconds = prev + 1;
        
        // Show popup after 10 seconds
        if (newSeconds === 10 && !isVisible) {
          setIsVisible(true);
          analytics.track('newsletter_popup_shown', {
            trigger: 'timer',
            seconds_on_site: 10
          });
        }
        
        return newSeconds;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible, analytics]);

  const handleClose = () => {
    setIsVisible(false);
    localStorage.setItem('newsletter-popup-seen', 'true');
    localStorage.setItem('newsletter-popup-last-shown', Date.now().toString());
    
    analytics.track('newsletter_popup_closed', {
      seconds_shown: secondsElapsed - 10,
      action: 'close_button'
    });
  };

  const handleMaybeLater = () => {
    setIsVisible(false);
    localStorage.setItem('newsletter-popup-seen', 'true');
    localStorage.setItem('newsletter-popup-last-shown', Date.now().toString());
    
    analytics.track('newsletter_popup_closed', {
      seconds_shown: secondsElapsed - 10,
      action: 'maybe_later'
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name,
          source: 'popup'
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Velkommen ombord! 🎉",
          description: "Du er nu tilmeldt mit nyhedsbrev. Tjek din indbakke!",
        });
        
        setIsVisible(false);
        localStorage.setItem('newsletter-subscribed', 'true');
        localStorage.setItem('newsletter-popup-seen', 'true');
        
        analytics.track('newsletter_subscribed', {
          source: 'popup',
          seconds_on_site: secondsElapsed,
          has_name: !!name
        });
      } else {
        throw new Error(data.error || 'Subscription failed');
      }
    } catch (error) {
      toast({
        title: "Ups! Noget gik galt",
        description: error instanceof Error ? error.message : "Prøv venligst igen senere",
        variant: "destructive",
      });
      
      analytics.track('newsletter_subscription_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'popup'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop for mobile */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={handleClose}
      />
      
      {/* Slide-in component */}
      <div className="fixed bottom-4 right-4 z-50 w-full max-w-sm md:max-w-md animate-slide-in">
        <div className="neo-card p-6 bg-white relative">
          {/* Close button */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center neo-border bg-white hover:bg-gray-50 hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all"
            aria-label="Close"
          >
            <X className="w-4 h-4" />
          </button>

          {/* Timer display */}
          <div className="mb-4 flex items-center gap-2 text-sm text-gray-600">
            <Clock className="w-4 h-4" />
            <span>Du har været her i {secondsElapsed} sekunder</span>
          </div>

          {/* Content */}
          <div className="mb-6">
            <h3 className="text-2xl font-bold mb-2">
              Hold dig opdateret! 🚀
            </h3>
            <p className="text-gray-600">
              Få mine bedste marketing indsigter, case studies og lejlighedsvise dad jokes leveret til din indbakke. Ingen spam, kun værdi.
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="text"
                placeholder="Dit navn (valgfrit)"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full neo-input"
              />
            </div>
            
            <div>
              <input
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full neo-input"
              />
            </div>

            <div className="flex gap-3">
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 neo-button bg-primary hover:bg-primary/90 text-black disabled:opacity-50"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center gap-2">
                    <span className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                    Tilmelder...
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-2">
                    <Mail className="w-4 h-4" />
                    Tilmeld
                  </span>
                )}
              </button>
              
              <button
                type="button"
                onClick={handleMaybeLater}
                className="neo-button bg-white hover:bg-gray-50"
              >
                Måske senere
              </button>
            </div>
          </form>

          {/* Trust text */}
          <p className="text-xs text-gray-500 mt-4 text-center">
            Tilmeld dig 500+ marketers. Afmeld når som helst.
          </p>
        </div>
      </div>
    </>
  );
};

export default NewsletterSlideIn;