import React, { useState, useEffect } from 'react';
import { ArrowLeft, Star, Check, Clock, Users } from 'lucide-react';
import { Link, useParams } from 'react-router-dom';
import { Service, getTranslatedServiceById } from '../../data/services';
import ServiceCheckout from './ServiceCheckout';
import ServiceRequest from './ServiceRequest';
import { useScrollReveal } from '../../hooks/useScrollReveal';
import { useAnalytics } from '../../services/analytics';
import { useTranslation } from 'react-i18next';

interface ServiceSingleContentProps {
  service: Service;
}

const ServiceSingleContent: React.FC<ServiceSingleContentProps> = ({ service: initialService }) => {
  const [currentStep, setCurrentStep] = useState<'overview' | 'action'>('overview');
  const [service, setService] = useState<Service>(initialService);
  const { serviceId } = useParams<{ serviceId: string }>();
  const { i18n } = useTranslation();
  const IconComponent = service.icon;
  const analytics = useAnalytics();

  // Initialize scroll reveal animations
  useScrollReveal();

  // Update service when language changes
  useEffect(() => {
    if (serviceId) {
      const translatedService = getTranslatedServiceById(serviceId);
      if (translatedService) {
        setService(translatedService);
      }
    }
  }, [serviceId, i18n.language]);

  // Track service view on component mount
  useEffect(() => {
    analytics.track('service_viewed', {
      service_id: service.id,
      service_name: service.title,
      service_price: typeof service.price === 'number' ? service.price : undefined,
    });
  }, [service, analytics]);

  const formatPrice = (price: number | 'custom', currency: string) => {
    if (price === 'custom') return 'Custom Quote';
    if (currency === 'DKK') {
      return `${price.toLocaleString('da-DK')} kr. ekskl. moms`;
    }
    return `$${price.toLocaleString()} ${currency}`;
  };

  const renderActionComponent = () => {
    if (service.type === 'one-off') {
      return <ServiceCheckout service={service} onBack={() => setCurrentStep('overview')} />;
    } else {
      return <ServiceRequest service={service} onBack={() => setCurrentStep('overview')} />;
    }
  };

  if (currentStep === 'action') {
    return (
      <div className="min-h-screen bg-gray-50 pt-32 pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Form */}
              <div className="lg:col-span-2">
                {renderActionComponent()}
              </div>

              {/* Right Column - Service Summary */}
              <div className="lg:col-span-1">
                <ServiceSummary service={service} />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 pb-20 overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link 
            to="/services" 
            className="flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Link>
        </div>

        {/* Service Header */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="flex items-center mb-4">
              <div 
                className="w-16 h-16 flex items-center justify-center neo-border mr-4"
                style={{ backgroundColor: service.color }}
              >
                <IconComponent size={32} className="text-black" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold">{service.title}</h1>
                {service.popular && (
                  <div className="flex items-center mt-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="text-sm font-medium text-yellow-600">Most Popular</span>
                  </div>
                )}
              </div>
            </div>

            <p className="text-lg text-gray-600 mb-6">{service.fullDescription}</p>

            <div className="flex flex-wrap gap-4 mb-8">
              <div className="neo-card p-4 bg-white">
                <div className="text-2xl font-bold text-primary mb-1">
                  {formatPrice(service.price, service.currency)}
                </div>
                <div className="text-sm text-gray-600">
                  {service.type === 'one-off' ? 'One-time payment' : 'Custom pricing'}
                </div>
              </div>
              
              <div className="neo-card p-4 bg-white">
                <div className="text-2xl font-bold text-secondary mb-1 flex items-center">
                  <Clock className="h-6 w-6 mr-2" />
                  {service.deliveryTime}
                </div>
                <div className="text-sm text-gray-600">Delivery time</div>
              </div>
            </div>

            <button
              onClick={() => {
                analytics.track('service_checkout_started', {
                  service_id: service.id,
                  service_name: service.title,
                  service_price: typeof service.price === 'number' ? service.price : undefined,
                });
                setCurrentStep('action');
              }}
              className={`neo-button px-8 py-4 text-lg font-bold ${
                service.type === 'one-off'
                  ? 'bg-primary hover:bg-primary/90 text-black'
                  : 'bg-secondary hover:bg-secondary/90 text-white'
              }`}
            >
              {service.type === 'one-off' ? 'Buy Now' : 'Request Quote'}
            </button>
          </div>

          {/* Service Features */}
          <div className="space-y-6">
            <div className="neo-card p-6 bg-white">
              <h3 className="text-xl font-bold mb-4">What's Included</h3>
              <ul className="space-y-3">
                {Array.isArray(service.includes) && service.includes.length > 0 ? (
                  service.includes.map((item, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span>{item}</span>
                    </li>
                  ))
                ) : (
                  <li className="text-gray-600">No items included</li>
                )}
              </ul>
            </div>

            {service.testimonial && (
              <div className="neo-card p-6 bg-primary">
                <div className="flex items-center mb-3">
                  {service.testimonial.rating && typeof service.testimonial.rating === 'number' ? (
                    [...Array(Math.max(0, Math.min(5, service.testimonial.rating)))].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))
                  ) : null}
                </div>
                <blockquote className="text-lg font-medium mb-3">
                  "{service.testimonial.quote}"
                </blockquote>
                <cite className="text-sm">
                  <strong>{service.testimonial.author}</strong>
                  <br />
                  {service.testimonial.company}
                </cite>
              </div>
            )}
          </div>
        </div>

        {/* Process Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Our Process</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {Array.isArray(service.process) && service.process.length > 0 ? (
              service.process.map((step, index) => (
                <div key={index} className="text-center">
                  <div
                    className="w-16 h-16 flex items-center justify-center neo-border bg-white text-2xl font-bold mb-4 mx-auto"
                    style={{ transform: `rotate(${index % 2 === 0 ? '3' : '-3'}deg)` }}
                  >
                    {index + 1}
                  </div>
                  <p className="text-sm">{step}</p>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-600">No process steps available</p>
              </div>
            )}
          </div>
        </div>

        {/* Features Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.isArray(service.features) && service.features.length > 0 ? (
              service.features.map((feature, index) => (
                <div
                  key={index}
                  className="neo-card p-6 bg-white text-center"
                  style={{
                    backgroundColor: index % 3 === 0 ? '#FFFFFF' :
                                   index % 3 === 1 ? service.color :
                                   '#F3F4F6',
                    transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
                  }}
                >
                  <h3 className="font-bold mb-2">{feature}</h3>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-600">No features available</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Service Summary Component for the sidebar
interface ServiceSummaryProps {
  service: Service;
}

const ServiceSummary: React.FC<ServiceSummaryProps> = ({ service }) => {
  const formatPrice = (price: number | 'custom', currency: string) => {
    if (price === 'custom') return 'Custom Quote';
    if (currency === 'DKK') {
      return `${price.toLocaleString('da-DK')} kr. ekskl. moms`;
    }
    return `$${price.toLocaleString()} ${currency}`;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-8">
      <h2 className="text-xl font-bold mb-6 text-blue-600">Service Summary</h2>
      
      <div className="border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-lg">{service.title}</h3>
          <span className="text-xl font-bold text-blue-600">
            {formatPrice(service.price, service.currency)}
          </span>
        </div>
        
        <div className="mb-4">
          <div className="w-20 h-16 bg-gray-100 rounded border flex items-center justify-center">
            <service.icon size={24} />
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-3">Includes:</h4>
          <ul className="space-y-2">
            {Array.isArray(service.includes) && service.includes.length > 0 ? (
              <>
                {service.includes.slice(0, 4).map((item, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <div className="w-1.5 h-1.5 bg-black rounded-full mr-3"></div>
                    {item}
                  </li>
                ))}
                {service.includes.length > 4 && (
                  <li className="text-sm text-gray-500">
                    +{service.includes.length - 4} more features
                  </li>
                )}
              </>
            ) : (
              <li className="text-sm text-gray-600">No items included</li>
            )}
          </ul>
        </div>
      </div>

      <div className="flex justify-between items-center py-3 border-b border-gray-200">
        <span className="text-sm text-gray-600">Delivery Time</span>
        <span className="font-medium">{service.deliveryTime}</span>
      </div>

      <div className="flex justify-between items-center py-4 border-b border-gray-200">
        <span className="text-lg font-bold">Total</span>
        <span className="text-xl font-bold text-blue-600">
          {formatPrice(service.price, service.currency)}
        </span>
      </div>

      {service.price !== 'custom' && (
        <p className="text-xs text-gray-500 text-right mt-2">{service.currency === 'DKK' ? 'Alle priser ekskl. moms' : `All prices in ${service.currency}`}</p>
      )}
    </div>
  );
};

export default ServiceSingleContent;
