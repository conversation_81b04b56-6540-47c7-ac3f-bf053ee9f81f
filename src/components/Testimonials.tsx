
import React from 'react';
import { useTranslation } from 'react-i18next';

const Testimonials = () => {
  const { t } = useTranslation('home');
  const testimonials = [
    {
      quote: "Asger tog vores kaotiske marketing setup og gav os en klar 4-ugers plan. Nu ved vi præcis hvad der virker og hvorfor. <PERSON> evne til at oversætte mellem tech og business er unik.",
      author: "<PERSON>",
      position: "CEO",
      company: "FashionLab"
    },
    {
      quote: "Vi brændte 50k af om måneden på LinkedIn ads uden resultater. Asger stoppede blødningen, snakkede med vores kunder, og nu får vi 30+ kvalificerede leads om ugen til halv pris.",
      author: "<PERSON>",
      position: "Co-founder",
      company: "KUBO Education"
    },
    {
      quote: "Endelig en marketing-rådgiver der faktisk har prøvet det selv. <PERSON><PERSON> har brugt millionerne, lavet fejlene, og ved præcis hvad der virker. Ingen BS, bare resultater.",
      author: "<PERSON>",
      position: "Founder",
      company: "GrowthTech Solutions"
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-blue text-white font-bold mb-4 rotate-1">
            {t('testimonials.badge')}
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            {t('testimonials.title')}
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="neo-card p-6 bg-white relative"
              style={{ 
                transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
              }}
            >
              {/* Quote mark */}
              <div className="absolute -top-5 -right-2 w-12 h-12 flex items-center justify-center neo-border bg-primary text-3xl font-serif rotate-12">
                "
              </div>
              
              <p className="mb-6 text-lg">{testimonial.quote}</p>
              
              <div>
                <p className="font-bold">{testimonial.author}</p>
                <p className="text-sm">{testimonial.position}, {testimonial.company}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
