
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Lightbulb, Target, Zap, Clock } from 'lucide-react';

const Benefits = () => {
  const { t } = useTranslation('home');

  const benefits = [
    {
      icon: <Lightbulb size={40} />,
      title: t('benefits.items.0.title'),
      description: t('benefits.items.0.description')
    },
    {
      icon: <Target size={40} />,
      title: t('benefits.items.1.title'),
      description: t('benefits.items.1.description')
    },
    {
      icon: <Zap size={40} />,
      title: t('benefits.items.2.title'),
      description: t('benefits.items.2.description')
    },
    {
      icon: <Clock size={40} />,
      title: t('benefits.items.3.title'),
      description: t('benefits.items.3.description')
    }
  ];

  return (
    <section id="about" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
            {t('benefits.badge')}
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            {t('benefits.title')}
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const colorClasses = index % 4 === 0 ? 'bg-white text-black' :
                               index % 4 === 1 ? 'bg-primary text-black' :
                               index % 4 === 2 ? 'bg-secondary text-white' :
                               'bg-accent text-white';
            
            return (
              <div 
                key={index}
                className={`neo-card p-6 ${colorClasses} hover:-translate-y-2 hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] cursor-pointer transition-all duration-200 focus:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] focus:outline-none focus-visible:ring-4 focus-visible:ring-primary`}
                style={{
                  transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
                }}
                tabIndex={0}
                role="article"
                aria-label={benefit.title}
              >
                <div className="mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
                <p>{benefit.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
