import React from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, MessageSquare, Rocket } from 'lucide-react';

const SimplifiedProcess = () => {
  const { t } = useTranslation('home');

  const steps = [
    {
      number: '1',
      icon: <Calendar className="w-8 h-8" />,
      title: t('simpleProcess.steps.book.title'),
      description: t('simpleProcess.steps.book.description'),
      color: 'bg-primary',
      rotation: '-rotate-2'
    },
    {
      number: '2',
      icon: <MessageSquare className="w-8 h-8" />,
      title: t('simpleProcess.steps.talk.title'),
      description: t('simpleProcess.steps.talk.description'),
      color: 'bg-accent',
      rotation: 'rotate-1'
    },
    {
      number: '3',
      icon: <Rocket className="w-8 h-8" />,
      title: t('simpleProcess.steps.execute.title'),
      description: t('simpleProcess.steps.execute.description'),
      color: 'bg-blue',
      rotation: '-rotate-1'
    }
  ];

  return (
    <section className="py-32 bg-white relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-1 bg-gray-200 hidden md:block" />
      
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-blue text-white font-bold mb-6 rotate-2">
            {t('simpleProcess.badge')}
          </span>
          <h2 className="text-4xl md:text-6xl font-bold mb-4">
            {t('simpleProcess.title')}
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto relative">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              {/* Connector line on mobile */}
              {index < steps.length - 1 && (
                <div className="absolute top-full left-1/2 -translate-x-1/2 w-1 h-8 bg-gray-200 md:hidden" />
              )}
              
              <div className={`neo-card p-8 bg-white ${step.rotation} hover:rotate-0 transition-transform duration-300 relative z-10`}>
                {/* Step number */}
                <div className={`absolute -top-4 -right-4 w-12 h-12 ${step.color} neo-border flex items-center justify-center text-white font-bold text-xl rotate-12`}>
                  {step.number}
                </div>
                
                {/* Icon */}
                <div className={`w-16 h-16 ${step.color} neo-border flex items-center justify-center text-white mb-6`}>
                  {step.icon}
                </div>
                
                {/* Content */}
                <h3 className="text-xl font-bold mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('simpleProcess.footer')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default SimplifiedProcess;