import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingDown, Users, Banknote } from 'lucide-react';

const ProblemSection = () => {
  const { t } = useTranslation('home');

  const problems = [
    {
      icon: <Banknote className="w-8 h-8" />,
      title: t('problem.items.waste.title'),
      description: t('problem.items.waste.description'),
      color: 'bg-red-500',
      rotation: 'rotate-2'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: t('problem.items.confusion.title'),
      description: t('problem.items.confusion.description'),
      color: 'bg-blue',
      rotation: '-rotate-1'
    },
    {
      icon: <TrendingDown className="w-8 h-8" />,
      title: t('problem.items.paralysis.title'),
      description: t('problem.items.paralysis.description'),
      color: 'bg-accent',
      rotation: 'rotate-1'
    }
  ];

  return (
    <section id="problem" className="py-32 bg-muted relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-red-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-blue/10 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-red-500 text-white font-bold mb-6 -rotate-1">
            {t('problem.badge')}
          </span>
          <h2 className="text-4xl md:text-6xl font-bold mb-4">
            {t('problem.title')}
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {problems.map((problem, index) => (
            <div
              key={index}
              className={`neo-card p-8 bg-white ${problem.rotation} hover:rotate-0 transition-transform duration-300`}
            >
              <div className={`w-16 h-16 ${problem.color} neo-border flex items-center justify-center text-white mb-6 -rotate-6`}>
                {problem.icon}
              </div>
              
              <h3 className="text-xl font-bold mb-3">
                {problem.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {problem.description}
              </p>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-600">
            {t('problem.footer')}
          </p>
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;