
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAnalytics } from '../services/analytics';
import SEOHead from './SEOHead';

interface HeroProps {
  onBookingClick: () => void;
}

const Hero: React.FC<HeroProps> = ({ onBookingClick }) => {
  const analytics = useAnalytics();
  const { t } = useTranslation('home');

  const handleIntroBookingClick = () => {
    analytics.track('cta_clicked', {
      cta_text: t('hero.cta.primary'),
      cta_location: 'hero_section',
      destination: '/booking/intro',
    });
    // Navigate to intro booking
    window.location.href = '/booking/intro';
  };

  const handleFullBookingClick = () => {
    analytics.track('cta_clicked', {
      cta_text: t('hero.cta.secondary'),
      cta_location: 'hero_section',
      destination: '/booking',
    });
    onBookingClick();
  };



  return (
    <>
      <SEOHead
        title={t('hero.title')}
        description={t('hero.description')}
      />
      <section className="pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden relative">
      <div className="container mx-auto px-4 relative">
        {/* Mobile decorative elements */}
        <div className="absolute top-5 right-5 w-16 h-16 bg-accent neo-border rotate-12 opacity-30 md:hidden animate-float" />
        <div className="absolute bottom-10 left-5 w-20 h-20 bg-primary neo-border -rotate-6 opacity-30 md:hidden animate-float [animation-delay:1s]" />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
          <div className="relative z-10 max-w-xl">
            <div className="animate-slide-in [animation-delay:0.1s] opacity-0">
              <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                {t('hero.badge')}
              </span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6 animate-slide-in [animation-delay:0.2s] opacity-0">
              {t('hero.title').split(' ').slice(0, -1).join(' ')} <span className="relative inline-block">
                {t('hero.title').split(' ').slice(-1)[0]}
                <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                  <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                </svg>
              </span>
            </h1>

            <p className="text-xl mb-8 animate-slide-in [animation-delay:0.3s] opacity-0">
              {t('hero.description')}
            </p>
            
            <div className="space-y-4 animate-slide-in [animation-delay:0.4s] opacity-0">
              {/* Primary CTA - Free intro */}
              <button
                onClick={handleIntroBookingClick}
                className="neo-button bg-primary hover:bg-primary/90 text-black text-lg px-8 py-4 w-full sm:w-auto animate-pulse"
              >
                {t('hero.cta.primary')}
              </button>
              
              {/* Secondary CTA - Full session */}
              <div className="text-center">
                <span className="text-sm text-gray-600 mr-2">eller</span>
                <button
                  onClick={handleFullBookingClick}
                  className="text-primary underline hover:no-underline"
                >
                  {t('hero.cta.secondary')}
                </button>
              </div>
            </div>
            
            {/* Urgency message */}
            <div className="mt-6 animate-slide-in [animation-delay:0.5s] opacity-0">
              <p className="text-red-600 font-semibold flex items-center gap-2">
                {t('hero.urgency')}
              </p>
            </div>
            
            {/* Trust signals */}
            <div className="mt-4 animate-slide-in [animation-delay:0.6s] opacity-0">
              <p className="text-sm text-gray-600">
                {t('hero.trust')}
              </p>
            </div>
          </div>
          
          <div className="relative hidden lg:block">
            {/* Background decorative elements - more dynamic placement */}
            <div className="absolute -top-20 -left-16 w-48 h-48 bg-primary neo-border rotate-12 animate-float opacity-80"></div>
            <div className="absolute top-1/3 -right-10 w-32 h-32 bg-accent neo-border -rotate-6 animate-float [animation-delay:1.5s] opacity-70"></div>
            <div className="absolute -bottom-16 left-10 w-40 h-40 bg-blue neo-border rotate-45 animate-float [animation-delay:2s] opacity-60"></div>
            <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-pink neo-border -rotate-12 animate-float [animation-delay:0.5s] opacity-50"></div>
            
            {/* Main image container */}
            <div className="relative z-10 max-w-md mx-auto">
              {/* Direct image without frame */}
              <img 
                src="/nerd.jpeg" 
                alt="Asger working on marketing strategies" 
                className="w-full h-auto neo-border rotate-3 animate-fade-in shadow-[12px_12px_0px_0px_rgba(0,0,0,1)]"
              />
              
              {/* Additional floating elements around the image */}
              <div className="absolute -top-8 -right-8 bg-primary rounded-full w-16 h-16 neo-border animate-pulse"></div>
              <div className="absolute -bottom-6 -left-6 bg-accent rounded-full w-12 h-12 neo-border animate-bounce"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </>
  );
};

export default Hero;
