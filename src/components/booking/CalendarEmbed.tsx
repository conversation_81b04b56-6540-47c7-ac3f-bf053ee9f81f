import React, { useEffect, useRef } from 'react';
import { createCalComIntegration, defaultCalComConfig } from '../../integrations/calendar/cal-com';

interface CalendarEmbedProps {
  onBookingComplete?: (event: any) => void;
  prefillData?: {
    name?: string;
    email?: string;
    notes?: string;
    customAnswers?: Record<string, any>;
  };
}

declare global {
  interface Window {
    Cal: any;
  }
}

const CalendarEmbed: React.FC<CalendarEmbedProps> = ({ onBookingComplete, prefillData }) => {
  const calRef = useRef<HTMLDivElement>(null);
  const embedInitialized = useRef(false);

  useEffect(() => {
    // Prevent double initialization
    if (embedInitialized.current) return;
    embedInitialized.current = true;

    // Load Cal.com embed script
    const script = document.createElement('script');
    script.src = 'https://app.cal.com/embed/embed.js';
    script.async = true;
    document.head.appendChild(script);

    script.onload = () => {
      if (window.Cal && calRef.current) {
        const calIntegration = createCalComIntegration(
          defaultCalComConfig.username,
          defaultCalComConfig.eventTypeSlug
        );

        // Initialize inline embed
        window.Cal('inline', {
          elementOrSelector: calRef.current,
          calLink: calIntegration.getEmbedUrl(),
          config: {
            theme: 'light',
            styles: {
              branding: {
                brandColor: '#000000',
              },
            },
            ...(prefillData && {
              name: prefillData.name,
              email: prefillData.email,
              notes: prefillData.notes,
              customAnswers: prefillData.customAnswers,
            }),
          },
        });

        // Listen for booking events
        window.Cal('on', {
          action: 'bookingSuccessful',
          callback: (event: any) => {
            console.log('Booking successful:', event);
            if (onBookingComplete) {
              onBookingComplete(event);
            }
          },
        });
      }
    };

    return () => {
      // Cleanup
      const scripts = document.querySelectorAll('script[src*="cal.com"]');
      scripts.forEach(script => script.remove());
    };
  }, [onBookingComplete, prefillData]);

  return (
    <div className="cal-embed-container">
      <div 
        ref={calRef} 
        className="w-full min-h-[600px] neo-border bg-white"
        style={{ overflow: 'hidden' }}
      />
    </div>
  );
};

export default CalendarEmbed;