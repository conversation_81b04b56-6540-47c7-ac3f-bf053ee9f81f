
import React from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { CalendarDays, Clock, CheckCircle, DollarSign } from 'lucide-react';

const BookingInfo = () => {
  const { currentStep } = useFormContext();
  
  return (
    <div className="animate-fade-in">
      <h3 className="text-lg font-bold mb-4">Marketing Terapi Session</h3>
      
      {currentStep === 1 && (
        <div>
          <p className="mb-4 text-sm">
            60 minutters fokuseret marketing rådgivning tilpasset dine specifikke udfordringer.
          </p>
          
          <div className="space-y-3">
            <div className="flex items-start gap-2.5">
              <Clock className="text-primary mt-0.5" size={16} />
              <span className="text-sm">60 min. 1-til-1 session</span>
            </div>
            <div className="flex items-start gap-2.5">
              <CheckCircle className="text-primary mt-0.5" size={16} />
              <span className="text-sm">Konkrete lø<PERSON>ninger</span>
            </div>
            <div className="flex items-start gap-2.5">
              <DollarSign className="text-primary mt-0.5" size={16} />
              <span className="text-sm font-medium">1.500 kr + moms</span>
            </div>
          </div>
        </div>
      )}
      
      {currentStep === 2 && (
        <div>
          <h4 className="font-medium mb-3">Hvorfor disse spørgsmål?</h4>
          <p className="text-sm mb-4">
            Din virksomhedstype og branche hjælper mig med at forberede relevante råd til din session.
          </p>

          <div className="neo-border p-3 bg-primary/10">
            <p className="text-sm font-medium mb-2">Perfekt til:</p>
            <ul className="text-sm space-y-1">
              <li>• Virksomheder klar til vækst</li>
              <li>• Teams der søger strategisk retning</li>
              <li>• Ledere der vil implementere hurtigt</li>
            </ul>
          </div>
        </div>
      )}
      
      {currentStep === 3 && (
        <div>
          <h4 className="font-medium mb-3">Min ekspertise</h4>
          
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div className="neo-border p-2.5 text-center">
              <p className="text-xs font-medium">Tiltrækning</p>
              <p className="text-xs text-muted-foreground">Ads, SEO</p>
            </div>
            <div className="neo-border p-2.5 text-center">
              <p className="text-xs font-medium">Konvertering</p>
              <p className="text-xs text-muted-foreground">CRO, UX</p>
            </div>
            <div className="neo-border p-2.5 text-center">
              <p className="text-xs font-medium">Fastholdelse</p>
              <p className="text-xs text-muted-foreground">Email, CRM</p>
            </div>
            <div className="neo-border p-2.5 text-center">
              <p className="text-xs font-medium">Strategi</p>
              <p className="text-xs text-muted-foreground">Vækst, brand</p>
            </div>
          </div>
          
          <div className="neo-border p-3 bg-muted">
            <p className="text-xs italic">
              "43% reduktion i CAC efter én session!"
            </p>
            <p className="text-xs font-medium mt-1">— Sarah J., SaaS</p>
          </div>
        </div>
      )}
      
      {currentStep === 4 && (
        <div>
          <h4 className="font-medium mb-3">Forberedelse = værdi</h4>
          
          <div className="space-y-3 mb-4">
            <div className="neo-border p-2.5">
              <p className="text-sm font-medium">1. Specifikke spørgsmål</p>
              <p className="text-xs text-muted-foreground">Konkrete spørgsmål = bedre svar</p>
            </div>
            <div className="neo-border p-2.5">
              <p className="text-sm font-medium">2. Del data</p>
              <p className="text-xs text-muted-foreground">Analytics, kampagner, resultater</p>
            </div>
            <div className="neo-border p-2.5">
              <p className="text-sm font-medium">3. Tag noter</p>
              <p className="text-xs text-muted-foreground">60 min. fyldt med indsigter</p>
            </div>
          </div>
          
          <div className="neo-border p-3 bg-primary/10">
            <p className="text-xs font-medium mb-1">Næste skridt:</p>
            <p className="text-xs">Betaling → Tidsvalg → Kalenderinvitation</p>
          </div>
        </div>
      )}
      
      {currentStep === 5 && (
        <div>
          <h4 className="font-medium mb-3">Næsten klar!</h4>
          
          <div className="neo-border p-3 bg-primary/10 mb-4">
            <p className="text-lg font-bold">1.500 kr</p>
            <p className="text-xs text-muted-foreground">+ moms (1.875 kr total)</p>
          </div>
          
          <div className="space-y-2 mb-4">
            <div className="flex items-start gap-2">
              <CheckCircle className="text-primary mt-0.5" size={14} />
              <p className="text-xs">Sikker Stripe betaling</p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="text-primary mt-0.5" size={14} />
              <p className="text-xs">Vælg tid efter betaling</p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="text-primary mt-0.5" size={14} />
              <p className="text-xs">Øjeblikkelig bekræftelse</p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="text-primary mt-0.5" size={14} />
              <p className="text-xs">100% tilfredshedsgaranti</p>
            </div>
          </div>
          
          <div className="neo-border p-2 bg-muted">
            <p className="text-xs text-center text-muted-foreground">
              Handelsbetingelser gælder
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingInfo;
