import React, { useState } from 'react';
import { Calendar, Clock, CreditCard } from 'lucide-react';
import { Button } from '../ui/button';

interface GoogleCalendarEmbedMockProps {
  onBookingComplete?: () => void;
  prefillData?: {
    name?: string;
    email?: string;
    phone?: string;
    notes?: string;
  };
}

// Mock time slots for testing
const MOCK_SLOTS = [
  { date: '2025-01-10', time: '10:00', available: true },
  { date: '2025-01-10', time: '14:00', available: true },
  { date: '2025-01-11', time: '09:00', available: true },
  { date: '2025-01-11', time: '13:00', available: false },
  { date: '2025-01-12', time: '10:00', available: true },
  { date: '2025-01-12', time: '15:00', available: true },
];

const GoogleCalendarEmbedMock: React.FC<GoogleCalendarEmbedMockProps> = ({ 
  onBookingComplete, 
  prefillData 
}) => {
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [showPayment, setShowPayment] = useState(false);

  const handleSlotSelect = (slotId: string) => {
    setSelectedSlot(slotId);
  };

  const handleProceedToPayment = () => {
    setShowPayment(true);
  };

  const handleMockPayment = () => {
    // In development, just call the completion handler
    // In production, this would redirect to Stripe
    if (onBookingComplete) {
      onBookingComplete();
    }
  };

  if (showPayment) {
    return (
      <div className="p-8 neo-border bg-white rounded-lg text-center">
        <CreditCard className="w-16 h-16 mx-auto mb-4" />
        <h3 className="text-xl font-bold mb-2">Booking simuleret!</h3>
        <p className="text-gray-600 mb-4">
          I produktion vil du nu kunne klikke på betalingsknappen nedenfor
        </p>
        <div className="p-4 bg-green-50 rounded mb-4">
          <p className="font-semibold">✓ Tid valgt</p>
          <p className="text-sm text-gray-600">Klik på "Gå til betaling" knappen nedenfor</p>
        </div>
        <button
          onClick={handleMockPayment}
          className="neo-button bg-primary text-white"
        >
          OK, forstået
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 neo-border bg-white rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <Calendar className="w-5 h-5" />
        <h3 className="font-semibold">Mock Google Calendar (Test Mode)</h3>
      </div>
      
      <div className="mb-4 p-3 bg-yellow-50 rounded text-sm">
        <p className="text-yellow-800">
          ⚠️ Dette er en mock version til test. I produktion vises den rigtige Google Calendar.
        </p>
      </div>

      <div className="space-y-2 mb-6">
        {MOCK_SLOTS.map((slot, index) => (
          <button
            key={index}
            onClick={() => slot.available && handleSlotSelect(`${slot.date}-${slot.time}`)}
            disabled={!slot.available}
            className={`w-full p-3 text-left neo-border rounded transition-all ${
              selectedSlot === `${slot.date}-${slot.time}`
                ? 'bg-primary text-white shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]'
                : slot.available
                ? 'bg-white hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                : 'bg-gray-100 opacity-50 cursor-not-allowed'
            }`}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">{slot.date}</div>
                <div className="text-sm flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {slot.time} - {parseInt(slot.time) + 2}:00
                </div>
              </div>
              {!slot.available && (
                <span className="text-sm text-red-600">Optaget</span>
              )}
            </div>
          </button>
        ))}
      </div>

      {selectedSlot && (
        <Button 
          onClick={handleProceedToPayment}
          className="w-full neo-button bg-primary"
        >
          Fortsæt til betaling
        </Button>
      )}

      {prefillData && (
        <div className="mt-4 p-3 bg-gray-50 rounded text-xs text-gray-600">
          <p className="font-semibold mb-1">Prefilled data:</p>
          <p>Navn: {prefillData.name}</p>
          <p>Email: {prefillData.email}</p>
        </div>
      )}
    </div>
  );
};

export default GoogleCalendarEmbedMock;