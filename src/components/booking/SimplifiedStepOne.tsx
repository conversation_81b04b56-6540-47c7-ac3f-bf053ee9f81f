import React, { useState, useEffect } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { AlertCircle } from 'lucide-react';

const MAIN_CHALLENGES = [
  { value: 'tracking', label: 'Vi ved ikke om vores marketing virker' },
  { value: 'strategy', label: 'Vi mangler en klar strategi' },
  { value: 'performance', label: 'Vores team/bureau performer ikke' },
  { value: 'scaling', label: 'Vi skal skalere men ved ikke hvordan' },
  { value: 'technology', label: 'Vi vil bruge teknologi/AI men ved ikke hvordan' },
  { value: 'other', label: 'Andet' }
];

const SimplifiedStepOne = () => {
  const { formData, updateFormData, errors } = useFormContext();
  const [availabilityData, setAvailabilityData] = useState<{
    thisWeek: number;
    nextAvailable: string | null;
  } | null>(null);
  
  // Fetch real-time availability
  useEffect(() => {
    const fetchAvailability = async () => {
      try {
        const apiUrl = process.env.NODE_ENV === 'development' 
          ? 'http://localhost:3000/api/availability-summary'
          : '/api/availability-summary';
        
        const response = await fetch(apiUrl);
        if (response.ok) {
          const data = await response.json();
          setAvailabilityData(data);
        }
      } catch (error) {
        console.error('Failed to fetch availability:', error);
        // Use fallback data
        setAvailabilityData({
          thisWeek: 3,
          nextAvailable: 'tirsdag kl. 10:00'
        });
      }
    };
    
    fetchAvailability();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Lad os starte med det basale</h2>
        <p className="text-gray-600">Og din primære udfordring</p>
        
        {/* Availability info */}
        {availabilityData && (
          <div className="mt-4 p-2 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm text-gray-600">
              {availabilityData.thisWeek} {availabilityData.thisWeek === 1 ? 'tid' : 'tider'} ledige denne uge
              {availabilityData.nextAvailable && ` • Næste ledige: ${availabilityData.nextAvailable}`}
            </p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName">Fornavn *</Label>
          <Input
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            className={errors.firstName ? 'border-red-500' : ''}
            placeholder="Anders"
          />
          {errors.firstName && (
            <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
          )}
        </div>

        <div>
          <Label htmlFor="lastName">Efternavn *</Label>
          <Input
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            className={errors.lastName ? 'border-red-500' : ''}
            placeholder="Andersen"
          />
          {errors.lastName && (
            <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="email">Email *</Label>
        <Input
          id="email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          className={errors.email ? 'border-red-500' : ''}
          placeholder="<EMAIL>"
        />
        {errors.email && (
          <p className="text-sm text-red-500 mt-1">{errors.email}</p>
        )}
      </div>

      <div>
        <Label htmlFor="companyName">Virksomhed *</Label>
        <Input
          id="companyName"
          name="companyName"
          value={formData.companyName}
          onChange={handleChange}
          className={errors.companyName ? 'border-red-500' : ''}
          placeholder="Din virksomheds navn"
        />
        {errors.companyName && (
          <p className="text-sm text-red-500 mt-1">{errors.companyName}</p>
        )}
      </div>

      <div>
        <Label htmlFor="phone">Telefon (valgfri)</Label>
        <Input
          id="phone"
          name="phone"
          type="tel"
          value={formData.phone || ''}
          onChange={handleChange}
          placeholder="+45 12 34 56 78"
        />
        <p className="text-sm text-gray-500 mt-1">Hvis du foretrækker at blive ringet op</p>
      </div>

      <div className="pt-4 border-t">
        <Label className="text-lg font-semibold mb-4 block">
          Hvad er din primære marketing-udfordring? *
        </Label>
        <RadioGroup
          value={formData.primaryChallenge || ''}
          onValueChange={(value) => updateFormData({ primaryChallenge: value })}
        >
          {MAIN_CHALLENGES.map((challenge) => (
            <Label
              key={challenge.value}
              htmlFor={challenge.value}
              className={`flex items-start space-x-3 mb-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 group ${
                formData.primaryChallenge === challenge.value 
                  ? 'border-primary bg-primary/5' 
                  : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
              }`}
            >
              <RadioGroupItem 
                value={challenge.value} 
                id={challenge.value}
                className="mt-0.5"
              />
              <span className="flex-1 text-base group-hover:text-black transition-colors">
                {challenge.label}
              </span>
            </Label>
          ))}
        </RadioGroup>
        {errors.primaryChallenge && (
          <p className="text-sm text-red-500 mt-2">{errors.primaryChallenge}</p>
        )}

        {formData.primaryChallenge === 'other' && (
          <div className="mt-4">
            <Input
              placeholder="Beskriv kort din udfordring..."
              value={formData.otherChallenge || ''}
              onChange={(e) => updateFormData({ otherChallenge: e.target.value })}
              className="mt-2"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplifiedStepOne;