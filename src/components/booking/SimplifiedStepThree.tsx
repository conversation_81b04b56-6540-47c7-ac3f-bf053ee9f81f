import React, { useState, useEffect, useRef } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Checkbox } from '../ui/checkbox';
import { CreditCard, Loader2 } from 'lucide-react';
import TimeSlotPicker from './TimeSlotPicker';
import { toast } from '../../hooks/use-toast';
import { format } from 'date-fns';
import { da } from 'date-fns/locale';
import { createConsultationCheckout } from '../../services/stripeCheckoutService';

const SimplifiedStepThree = () => {
  const { formData, updateFormData, errors } = useFormContext();
  const [agreedToTerms, setAgreedToTerms] = useState(formData.agreedToTerms || false);
  const [understoodPreparation, setUnderstoodPreparation] = useState(formData.understoodPreparation || false);
  const [selectedSlot, setSelectedSlot] = useState<{ date: Date; time: string; available: boolean; isWeekend?: boolean } | null>(null);
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false);
  const paymentSectionRef = useRef<HTMLDivElement>(null);
  
  // Auto-scroll to payment section when slot is selected
  useEffect(() => {
    if (selectedSlot && paymentSectionRef.current) {
      setTimeout(() => {
        paymentSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
    }
  }, [selectedSlot]);

  // Handle booking and payment through Stripe Checkout Sessions
  const handleBookAndPay = async () => {
    if (!selectedSlot) {
      toast({
        title: "Vælg venligst et tidspunkt",
        description: "Du skal vælge et tidspunkt før du kan fortsætte til betaling",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingCheckout(true);

    try {
      // Format the selected date and time for the booking
      const bookingDateTime = format(selectedSlot.date, 'yyyy-MM-dd') + ' ' + selectedSlot.time;
      
      // Update form data with booking info
      updateFormData({ 
        sessionDate: bookingDateTime,
        agreedToTerms,
        understoodPreparation
      });

      // Create Stripe checkout session with booking metadata
      const checkoutData = {
        formData: {
          ...formData,
          sessionDate: bookingDateTime,
          bookingInfo: {
            date: format(selectedSlot.date, 'EEEE d. MMMM yyyy', { locale: da }),
            time: selectedSlot.time,
            duration: '2 timer'
          }
        },
        serviceId: 'marketing-therapy-session',
        priceInDKK: selectedSlot.isWeekend ? 6000 : 3000, // Double price for weekends
      };

      // This will redirect to Stripe Checkout
      await createConsultationCheckout(checkoutData);
    } catch (error) {
      console.error('Error creating checkout:', error);
      toast({
        title: "Kunne ikke oprette betaling",
        description: "Der opstod en fejl. Prøv venligst igen.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingCheckout(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Lad os booke din session</h2>
        <p className="text-gray-600">Og sikre vi får mest muligt ud af tiden</p>
      </div>

      <div className="space-y-4 p-4 bg-muted neo-border mb-6">
        <h3 className="font-semibold">Vigtigt at vide før du booker:</h3>
        
        <div className="text-sm text-gray-700">
          <p>💰 <strong>Priser:</strong></p>
          <ul className="ml-6 mt-1">
            <li>• Hverdage (man-fre): kr. 3.000,- ekskl. moms</li>
            <li>• Weekender (lør-søn): kr. 6.000,- ekskl. moms</li>
          </ul>
        </div>
        
        <div className="flex items-start space-x-3">
          <Checkbox
            id="terms"
            checked={agreedToTerms}
            onCheckedChange={(checked) => {
              setAgreedToTerms(checked as boolean);
              updateFormData({ agreedToTerms: checked as boolean });
            }}
          />
          <Label htmlFor="terms" className="font-normal cursor-pointer">
            Jeg forstår dette er en betalt session som betales efter booking og accepterer <a href="/terms" target="_blank" className="text-primary underline">handelsbetingelserne</a>
          </Label>
        </div>

        <div className="flex items-start space-x-3">
          <Checkbox
            id="preparation"
            checked={understoodPreparation}
            onCheckedChange={(checked) => {
              setUnderstoodPreparation(checked as boolean);
              updateFormData({ understoodPreparation: checked as boolean });
            }}
          />
          <Label htmlFor="preparation" className="font-normal cursor-pointer">
            Jeg er klar til at give adgang til relevante marketing tools så vi kan kigge på data sammen
          </Label>
        </div>

        {(errors.agreedToTerms || errors.understoodPreparation) && (
          <p className="text-sm text-red-500">
            Du skal acceptere begge punkter for at fortsætte
          </p>
        )}
      </div>

      <div>
        <Label htmlFor="sessionGoals">
          Hvad håber du at få ud af vores session? *
        </Label>
        <Textarea
          id="sessionGoals"
          name="sessionGoals"
          value={formData.sessionGoals || ''}
          onChange={(e) => updateFormData({ sessionGoals: e.target.value })}
          className={errors.sessionGoals ? 'border-red-500' : ''}
          placeholder="Fx: Jeg vil gerne have en klar strategi for Q1 2025, forstå hvilke kanaler vi skal fokusere på..."
          rows={3}
        />
        {errors.sessionGoals && (
          <p className="text-sm text-red-500 mt-1">{errors.sessionGoals}</p>
        )}
      </div>

      <div>
        <Label htmlFor="specificFocus">
          Er der noget specifikt du gerne vil have vi kigger på? (valgfri)
        </Label>
        <Textarea
          id="specificFocus"
          name="specificFocus"
          value={formData.specificFocus || ''}
          onChange={(e) => updateFormData({ specificFocus: e.target.value })}
          placeholder="Fx: Vores Google Ads performance, LinkedIn strategi, conversion rate på landing pages..."
          rows={3}
        />
      </div>

      {/* Show time slot picker only if terms are agreed */}
      {agreedToTerms && understoodPreparation && (
        <div className="space-y-4">
          <div>
            <Label className="text-lg font-semibold mb-3 block">
              Vælg tidspunkt for din 2-timers session
            </Label>
            
            <TimeSlotPicker 
              onSlotSelect={setSelectedSlot}
              selectedSlot={selectedSlot}
            />
            
            {errors.sessionDate && (
              <p className="text-sm text-red-500 mt-2">{errors.sessionDate}</p>
            )}
          </div>

          {/* Show book and pay button when slot is selected */}
          {selectedSlot && (
            <div ref={paymentSectionRef} className="p-5 neo-border bg-primary/5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <p className="text-base font-medium mb-3">
                {format(selectedSlot.date, 'EEEE d. MMMM', { locale: da })} kl. {selectedSlot.time}
              </p>
              {selectedSlot.isWeekend && (
                <p className="text-sm text-blue-700 mb-3">
                  📅 Weekend pris (dobbelt takst)
                </p>
              )}
              <button
                onClick={handleBookAndPay}
                disabled={isCreatingCheckout}
                className="w-full neo-button bg-primary hover:bg-primary/90 text-black font-semibold py-3 flex items-center justify-center gap-2 disabled:opacity-50"
              >
                {isCreatingCheckout ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Opretter betaling...
                  </>
                ) : (
                  <>
                    <CreditCard className="w-5 h-5" />
                    Book og betal (kr. {selectedSlot.isWeekend ? '6.000,-' : '3.000,-'} ekskl. moms)
                  </>
                )}
              </button>
              <p className="text-xs text-gray-600 mt-2 text-center">
                Sikker betaling via Stripe
              </p>
            </div>
          )}
        </div>
      )}

      {!agreedToTerms || !understoodPreparation ? (
        <div className="p-4 bg-yellow-50 neo-border">
          <p className="text-sm text-yellow-800">
            ⚠️ Du skal acceptere betingelserne ovenfor før du kan vælge et tidspunkt
          </p>
        </div>
      ) : null}

    </div>
  );
};

export default SimplifiedStepThree;