import React, { useState, useEffect } from 'react';
import { format, addDays, startOfWeek, isSameDay, isAfter, isBefore, setHours, setMinutes } from 'date-fns';
import { da } from 'date-fns/locale';
import { Calendar, Clock, Loader2, AlertCircle } from 'lucide-react';
import { cn } from '../../lib/utils';

interface TimeSlot {
  date: Date;
  time: string;
  available: boolean;
  isWeekend?: boolean;
}

interface TimeSlotPickerProps {
  onSlotSelect: (slot: TimeSlot) => void;
  selectedSlot?: TimeSlot | null;
}

const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({ onSlotSelect, selectedSlot }) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [weeksToShow, setWeeksToShow] = useState(2);

  // Generate calendar days for the next N weeks
  const generateCalendarDays = () => {
    const days: (Date | null)[] = [];
    const today = new Date();
    const start = startOfWeek(today, { weekStartsOn: 1 }); // Start on Monday
    
    for (let week = 0; week < weeksToShow; week++) {
      for (let day = 0; day < 7; day++) {
        const date = addDays(start, week * 7 + day);
        // For the first week, add null for past dates to maintain grid alignment
        if (week === 0 && isBefore(date, today)) {
          days.push(null);
        } else if (isAfter(date, addDays(today, -1))) {
          days.push(date);
        }
      }
    }
    
    return days;
  };

  // Fetch available slots for selected date
  const fetchAvailableSlots = async (date: Date) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/available-slots?date=${format(date, 'yyyy-MM-dd')}`);
      
      if (!response.ok) {
        throw new Error('Kunne ikke hente ledige tider');
      }
      
      const data = await response.json();
      setAvailableSlots(data.slots || []);
      
      // Show message if it's a weekend
      if (data.message && data.slots.length === 0) {
        setError(data.message);
      }
    } catch (err) {
      console.error('Error fetching slots:', err);
      setError('Kunne ikke hente ledige tider. Prøv igen senere.');
      
      // For now, show mock data in development
      if (import.meta.env.DEV) {
        const mockSlots: TimeSlot[] = [
          { date, time: '09:00', available: true },
          { date, time: '10:00', available: true },
          { date, time: '11:00', available: false },
          { date, time: '12:00', available: true },
          { date, time: '13:00', available: true },
          { date, time: '14:00', available: false },
        ];
        setAvailableSlots(mockSlots);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch slots when date changes
  useEffect(() => {
    fetchAvailableSlots(selectedDate);
  }, [selectedDate]);

  const calendarDays = generateCalendarDays();
  const weekDays = ['Man', 'Tir', 'Ons', 'Tor', 'Fre', 'Lør', 'Søn'];

  return (
    <div className="space-y-6">
      {/* Calendar Grid */}
      <div className="neo-card p-6">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-5 h-5" />
          <h3 className="font-semibold text-lg">Vælg dato</h3>
        </div>

        {/* Week day headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekDays.map((day) => (
            <div key={day} className="text-center text-sm font-medium text-gray-600 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date, index) => {
            if (!date) {
              // Empty cell for past dates in the first week
              return <div key={index} className="aspect-square" />;
            }
            
            const isSelected = isSameDay(date, selectedDate);
            const isToday = isSameDay(date, new Date());
            const isWeekend = date.getDay() === 0 || date.getDay() === 6;
            
            return (
              <button
                key={index}
                onClick={() => setSelectedDate(date)}
                className={cn(
                  "aspect-square p-2 neo-border text-sm font-medium transition-all",
                  "hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]",
                  isSelected && "bg-primary text-white shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",
                  !isSelected && "bg-white",
                  isToday && !isSelected && "bg-yellow-50",
                  isWeekend && "text-blue-600 font-medium"
                )}
              >
                <div className="flex flex-col items-center justify-center h-full">
                  <span>{format(date, 'd')}</span>
                  {isToday && (
                    <span className="text-xs mt-1">I dag</span>
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Show more weeks button */}
        {weeksToShow < 6 && (
          <button
            onClick={() => setWeeksToShow(weeksToShow + 2)}
            className="w-full mt-4 neo-button bg-white hover:bg-muted text-sm"
          >
            Vis flere uger
          </button>
        )}
      </div>

      {/* Time Slots */}
      <div className="neo-card p-6">
        <div className="flex items-center gap-2 mb-4">
          <Clock className="w-5 h-5" />
          <h3 className="font-semibold text-lg">
            Ledige tider - {format(selectedDate, 'EEEE d. MMMM', { locale: da })}
          </h3>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        ) : error ? (
          <div className="flex items-center gap-2 text-red-600 py-4">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        ) : availableSlots.length === 0 ? (
          <p className="text-gray-600 py-4">
            {error || 'Ingen ledige tider denne dag. Vælg venligst en anden dato.'}
          </p>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {availableSlots.map((slot, index) => {
              const slotDateTime = setMinutes(
                setHours(slot.date, parseInt(slot.time.split(':')[0])),
                parseInt(slot.time.split(':')[1])
              );
              const isSelectedSlot = selectedSlot && 
                isSameDay(selectedSlot.date, slot.date) && 
                selectedSlot.time === slot.time;
              
              return (
                <button
                  key={index}
                  onClick={() => slot.available && onSlotSelect(slot)}
                  disabled={!slot.available}
                  className={cn(
                    "p-3 neo-border rounded text-center transition-all",
                    slot.available && "hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]",
                    isSelectedSlot && "bg-primary text-white shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",
                    !isSelectedSlot && slot.available && "bg-white",
                    !slot.available && "bg-gray-100 opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="font-medium">{slot.time}</div>
                  <div className="text-sm mt-1">
                    {slot.available ? (
                      slot.isWeekend ? '2 timer 💵' : '2 timer'
                    ) : (
                      'Optaget'
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        )}
      </div>

    </div>
  );
};

export default TimeSlotPicker;