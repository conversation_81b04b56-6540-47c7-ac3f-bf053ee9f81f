import React, { useEffect, useRef, useState } from 'react';
import { createGoogleCalendarIntegration, defaultGoogleCalendarConfig } from '../../integrations/calendar/google-calendar';
import { Loader2, Calendar } from 'lucide-react';

interface GoogleCalendarEmbedProps {
  onBookingComplete?: () => void;
  prefillData?: {
    name?: string;
    email?: string;
    phone?: string;
    notes?: string;
  };
}

const GoogleCalendarEmbed: React.FC<GoogleCalendarEmbedProps> = ({ onBookingComplete, prefillData }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const messageListenerAdded = useRef(false);

  useEffect(() => {
    if (!containerRef.current) return;

    const integration = createGoogleCalendarIntegration(defaultGoogleCalendarConfig);
    let appointmentUrl = integration.getAppointmentUrl();
    
    // Ensure we have a valid appointment URL, not just the base domain
    if (!appointmentUrl || appointmentUrl === 'https://calendar.google.com/calendar/appointments/schedules') {
      console.error('No valid Google Calendar appointment URL configured');
      setIsLoading(false);
      return;
    }

    // Add the required query parameter for Google Calendar
    const urlWithParams = appointmentUrl.includes('?') 
      ? appointmentUrl 
      : `${appointmentUrl}?gv=true`;

    // Create iframe
    const iframe = document.createElement('iframe');
    iframe.src = urlWithParams;
    iframe.style.border = '0';
    iframe.style.width = '100%';
    iframe.style.height = '650px';
    iframe.frameBorder = '0';
    iframe.onload = () => setIsLoading(false);

    // Clear container and add iframe
    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(iframe);

    // Listen for messages from the iframe (if Google implements postMessage)
    if (!messageListenerAdded.current) {
      messageListenerAdded.current = true;
      const handleMessage = (event: MessageEvent) => {
        // Check if message is from Google Calendar
        if (event.origin === 'https://calendar.google.com') {
          if (event.data.type === 'appointment-booked' && onBookingComplete) {
            onBookingComplete();
          }
        }
      };

      window.addEventListener('message', handleMessage);
      
      return () => {
        window.removeEventListener('message', handleMessage);
        messageListenerAdded.current = false;
      };
    }
  }, [onBookingComplete]);

  return (
    <div className="google-calendar-embed-container relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-600">Indlæser kalender...</p>
          </div>
        </div>
      )}
      <div 
        ref={containerRef} 
        className="w-full min-h-[650px] neo-border bg-white rounded-lg overflow-hidden"
      >
        {/* Show message if no calendar URL is configured */}
        {!import.meta.env.VITE_GOOGLE_CALENDAR_APPOINTMENT_URL && (
          <div className="flex items-center justify-center h-full p-8">
            <div className="text-center">
              <Calendar className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">Kalender ikke konfigureret</h3>
              <p className="text-gray-600">
                Google Calendar appointment URL mangler i miljøvariablerne.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GoogleCalendarEmbed;