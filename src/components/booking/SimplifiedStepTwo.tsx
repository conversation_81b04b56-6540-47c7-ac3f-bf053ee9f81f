import React, { useState, useEffect } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Loader2, Globe } from 'lucide-react';
import { toast } from '../../hooks/use-toast';

const BUDGET_RANGES = [
  { value: 'under_10k', label: 'Under 10.000 kr/måned' },
  { value: '10k_50k', label: '10.000 - 50.000 kr/måned' },
  { value: '50k_200k', label: '50.000 - 200.000 kr/måned' },
  { value: 'over_200k', label: 'Over 200.000 kr/måned' },
  { value: 'unknown', label: 'Ved ikke / Ikke fastlagt' }
];

const MARKETING_SETUP = [
  { value: 'self', label: 'Jeg gør det selv' },
  { value: 'internal', label: 'Internt team' },
  { value: 'agency', label: 'Eksternt bureau' },
  { value: 'combination', label: 'Kombination af ovenstående' },
  { value: 'none', label: 'Ingen rigtig ansvarlig' }
];

const SimplifiedStepTwo = () => {
  const { formData, updateFormData, errors } = useFormContext();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  
  // Auto-analyze website when URL is entered
  useEffect(() => {
    const analyzeWebsite = async () => {
      if (!formData.website || formData.website.trim() === '') return;
      
      // Check if URL is valid
      try {
        new URL(formData.website.startsWith('http') ? formData.website : `https://${formData.website}`);
      } catch (e) {
        return; // Invalid URL, don't analyze
      }
      
      // Skip if we've already analyzed this website
      if (formData.websiteAnalysis && analysisComplete) return;
      
      setIsAnalyzing(true);
      
      try {
        // Create an AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout
        
        const response = await fetch('/api/website-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: formData.website,
            mode: 'full' // Get full analysis including company name
          }),
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
          const analysis = await response.json();
          
          // Update form data with analysis results
          if (analysis.success) {
            updateFormData({
              websiteAnalysis: analysis,
              companyName: analysis.companyName || formData.companyName,
            });
            
            setAnalysisComplete(true);
            
            // Show toast if company name was found
            if (analysis.companyName && analysis.companyName !== formData.companyName) {
              toast({
                title: "Website analyseret",
                description: `Fandt virksomhed: ${analysis.companyName}`,
              });
            }
          }
        }
      } catch (error) {
        console.error('Error analyzing website:', error);
        // Silent fail - don't show error to user
      } finally {
        setIsAnalyzing(false);
      }
    };
    
    // Debounce the analysis to avoid too many requests
    const timer = setTimeout(() => {
      analyzeWebsite();
    }, 1500); // Wait 1.5 seconds after user stops typing
    
    return () => clearTimeout(timer);
  }, [formData.website]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Lad mig forstå jeres situation</h2>
        <p className="text-gray-600">Så jeg kan forberede mig bedst muligt</p>
      </div>

      <div>
        <Label htmlFor="website">
          Hvad er jeres website? (så jeg kan forberede mig) *
        </Label>
        <Input
          id="website"
          name="website"
          type="url"
          value={formData.website}
          onChange={(e) => updateFormData({ website: e.target.value })}
          className={errors.website ? 'border-red-500' : ''}
          placeholder="https://www.firma.dk"
        />
        {errors.website && (
          <p className="text-sm text-red-500 mt-1">{errors.website}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Jeg kigger på jeres site før vores session for at give bedre sparring
        </p>
        {isAnalyzing && (
          <div className="flex items-center gap-2 mt-2 text-sm text-blue-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Analyserer jeres website...</span>
          </div>
        )}
        {analysisComplete && !isAnalyzing && (
          <div className="flex items-center gap-2 mt-2 text-sm text-green-600">
            <Globe className="w-4 h-4" />
            <span>Website analyseret ✓</span>
          </div>
        )}
      </div>

      <div>
        <Label className="text-lg font-semibold mb-4 block">
          Hvem står for jeres marketing i dag? *
        </Label>
        <RadioGroup
          value={formData.marketingSetup || ''}
          onValueChange={(value) => updateFormData({ marketingSetup: value })}
        >
          {MARKETING_SETUP.map((setup) => (
            <Label
              key={setup.value}
              htmlFor={`setup-${setup.value}`}
              className={`flex items-start space-x-3 mb-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 group ${
                formData.marketingSetup === setup.value 
                  ? 'border-primary bg-primary/5' 
                  : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
              }`}
            >
              <RadioGroupItem 
                value={setup.value} 
                id={`setup-${setup.value}`}
                className="mt-0.5"
              />
              <span className="flex-1 text-base group-hover:text-black transition-colors">
                {setup.label}
              </span>
            </Label>
          ))}
        </RadioGroup>
        {errors.marketingSetup && (
          <p className="text-sm text-red-500 mt-2">{errors.marketingSetup}</p>
        )}
      </div>

      <div>
        <Label className="text-lg font-semibold mb-4 block">
          Kører I annoncering i dag? *
        </Label>
        <RadioGroup
          value={formData.runningAds || ''}
          onValueChange={(value) => {
            updateFormData({ runningAds: value });
            // Clear budget if they select 'no'
            if (value === 'no') {
              updateFormData({ marketingBudget: '' });
            }
          }}
        >
          <Label
            htmlFor="ads-yes"
            className={`flex items-start space-x-3 mb-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 group ${
              formData.runningAds === 'yes' 
                ? 'border-primary bg-primary/5' 
                : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
            }`}
          >
            <RadioGroupItem value="yes" id="ads-yes" className="mt-0.5" />
            <span className="flex-1 text-base group-hover:text-black transition-colors">
              Ja, vi kører annoncer
            </span>
          </Label>
          <Label
            htmlFor="ads-no"
            className={`flex items-start space-x-3 mb-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 group ${
              formData.runningAds === 'no' 
                ? 'border-primary bg-primary/5' 
                : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
            }`}
          >
            <RadioGroupItem value="no" id="ads-no" className="mt-0.5" />
            <span className="flex-1 text-base group-hover:text-black transition-colors">
              Nej, vi kører ikke annoncer
            </span>
          </Label>
        </RadioGroup>
        {errors.runningAds && (
          <p className="text-sm text-red-500 mt-2">{errors.runningAds}</p>
        )}
      </div>

      {/* Only show budget question if they're running ads */}
      {formData.runningAds === 'yes' && (
        <div>
          <Label className="text-lg font-semibold mb-4 block">
            Hvor meget bruger I på annoncering om måneden? *
          </Label>
          <RadioGroup
            value={formData.marketingBudget || ''}
            onValueChange={(value) => updateFormData({ marketingBudget: value })}
          >
            {BUDGET_RANGES.map((budget) => (
              <Label
                key={budget.value}
                htmlFor={`budget-${budget.value}`}
                className={`flex items-start space-x-3 mb-3 p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 group ${
                  formData.marketingBudget === budget.value 
                    ? 'border-primary bg-primary/5' 
                    : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
                }`}
              >
                <RadioGroupItem 
                  value={budget.value} 
                  id={`budget-${budget.value}`}
                  className="mt-0.5"
                />
                <span className="flex-1 text-base group-hover:text-black transition-colors">
                  {budget.label}
                </span>
              </Label>
            ))}
          </RadioGroup>
          {errors.marketingBudget && (
            <p className="text-sm text-red-500 mt-2">{errors.marketingBudget}</p>
          )}
        </div>
      )}

      <div>
        <Label htmlFor="challengeDescription">
          Beskriv jeres udfordring i 2-3 sætninger *
        </Label>
        <Textarea
          id="challengeDescription"
          name="challengeDescription"
          value={formData.challengeDescription || ''}
          onChange={(e) => updateFormData({ challengeDescription: e.target.value })}
          className={errors.challengeDescription ? 'border-red-500' : ''}
          placeholder="Fx: Vi får mange besøgende på sitet men ingen konverterer. Vi har prøvet Google Ads men det blev for dyrt..."
          rows={4}
        />
        {errors.challengeDescription && (
          <p className="text-sm text-red-500 mt-1">{errors.challengeDescription}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Jo mere specifik du er, jo bedre kan jeg hjælpe
        </p>
      </div>
    </div>
  );
};

export default SimplifiedStepTwo;