import React from 'react';
import { Helmet } from 'react-helmet-async';

const SecurityHeaders: React.FC = () => {
  return (
    <Helmet>
      {/* CSP should be set via HTTP headers only (in vercel.json) to avoid conflicts */}
      {/* Security Headers - Note: Some headers like CSP and X-Frame-Options work better as HTTP headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Permissions Policy */}
      <meta
        httpEquiv="Permissions-Policy"
        content="camera=(), microphone=(), geolocation=(), interest-cohort=()"
      />
      
      {/* Preload critical resources - Google Fonts handles font loading */}
      
      {/* Resource hints */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://eu.i.posthog.com" />
      
      {/* DNS prefetch for external domains */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://eu.i.posthog.com" />
      <link rel="dns-prefetch" href="https://api.stripe.com" />
    </Helmet>
  );
};

export default SecurityHeaders;
