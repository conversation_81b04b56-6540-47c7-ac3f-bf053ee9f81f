import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Calendar, Mail, Zap } from 'lucide-react';
import { useAnalytics } from '../services/analytics';

interface FinalCTAProps {
  onBookingClick?: () => void;
}

const FinalCTA: React.FC<FinalCTAProps> = ({ onBookingClick }) => {
  const { t } = useTranslation('home');
  const navigate = useNavigate();
  const analytics = useAnalytics();

  const handleBookingClick = () => {
    analytics.track('cta_clicked', {
      cta_text: 'Book 60 min session',
      cta_location: 'final_cta_section',
      destination: '/booking',
    });
    
    if (onBookingClick) {
      onBookingClick();
    } else {
      navigate('/booking');
    }
  };

  return (
    <section className="py-32 bg-gradient-to-br from-primary/10 to-accent/10 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-primary neo-border rotate-12 opacity-20 animate-float" />
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-accent neo-border -rotate-6 opacity-20 animate-float [animation-delay:2s]" />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue neo-border rotate-45 opacity-10" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <span className="inline-block px-4 py-2 neo-border bg-primary text-black font-bold mb-6 rotate-1">
            {t('finalCTA.badge')}
          </span>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            {t('finalCTA.title')}
          </h2>
          
          <p className="text-xl text-gray-700 mb-12">
            {t('finalCTA.description')}
          </p>

          <div className="neo-card bg-white p-8 md:p-12 max-w-xl mx-auto -rotate-1 hover:rotate-0 transition-transform duration-300">
            {/* Urgency message at top */}
            <div className="mb-6 p-3 bg-red-50 border-2 border-red-500 rounded-lg">
              <p className="text-red-600 font-bold text-center animate-pulse">
                {t('finalCTA.urgency')}
              </p>
            </div>
            
            {/* Primary CTA - Free intro */}
            <button
              onClick={() => {
                analytics.track('cta_clicked', {
                  cta_text: t('finalCTA.cta.primary'),
                  cta_location: 'final_cta_section',
                  destination: '/booking/intro',
                });
                navigate('/booking/intro');
              }}
              className="w-full neo-button bg-primary hover:bg-primary/90 text-black text-xl py-6 mb-4 group animate-pulse"
            >
              <span className="flex items-center justify-center gap-3">
                <Calendar className="w-6 h-6 group-hover:animate-bounce-light" />
                {t('finalCTA.cta.primary')}
                <Zap className="w-6 h-6 group-hover:animate-bounce-light [animation-delay:200ms]" />
              </span>
            </button>
            
            <div className="text-center mb-4">
              <span className="text-sm text-gray-600">eller</span>
            </div>
            
            {/* Secondary CTA - Full session */}
            <button
              onClick={handleBookingClick}
              className="w-full neo-button bg-white hover:bg-muted text-black text-lg py-4 mb-6 border-2 border-black"
            >
              {t('finalCTA.cta.secondary')}
            </button>
            
            <p className="text-sm text-gray-600 mb-6">
              {t('finalCTA.nextAvailable')}
            </p>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t-2 border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-gray-600">{t('finalCTA.or')}</span>
              </div>
            </div>
            
            <p className="mt-6 text-gray-600">
              {t('finalCTA.alternative.text')}
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 text-primary font-bold hover:underline mt-2"
            >
              <Mail className="w-4 h-4" />
              <EMAIL>
            </a>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-wrap justify-center gap-8 mt-12 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>{t('finalCTA.trust.available')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full" />
              <span>{t('finalCTA.trust.companies')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-accent rounded-full" />
              <span>{t('finalCTA.trust.satisfaction')}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;