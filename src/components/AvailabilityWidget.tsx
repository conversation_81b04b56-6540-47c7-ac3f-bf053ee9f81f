import React, { useState, useEffect } from 'react';
import { Calendar, AlertCircle, Clock } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface AvailabilityData {
  thisWeek: number;
  nextWeek: number;
  nextAvailable: string | null;
  totalAvailableThisMonth?: number;
  mock?: boolean;
}

const AvailabilityWidget: React.FC = () => {
  const [availability, setAvailability] = useState<AvailabilityData | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasBeenDismissed, setHasBeenDismissed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Don't show widget on booking pages or success pages
  const shouldShowWidget = !location.pathname.includes('/booking') && 
                           !location.pathname.includes('/success') &&
                           !location.pathname.includes('/privacy') &&
                           !location.pathname.includes('/terms');

  useEffect(() => {
    // Fetch real availability from API
    const fetchAvailability = async () => {
      try {
        setIsLoading(true);
        const apiUrl = '/api/availability-summary';
        
        const response = await fetch(apiUrl);
        
        if (response.ok) {
          const data = await response.json();
          setAvailability(data);
        } else {
          // Fallback to mock data if API fails
          console.error('Failed to fetch availability:', response.status);
          setAvailability({
            thisWeek: 2,
            nextWeek: 5,
            nextAvailable: 'i morgen kl. 10:00',
            mock: true
          });
        }
      } catch (error) {
        console.error('Error fetching availability:', error);
        // Fallback to mock data
        setAvailability({
          thisWeek: 2,
          nextWeek: 5,
          todayBookings: 1,
          nextAvailable: 'i morgen kl. 10:00',
          mock: true
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch
    fetchAvailability();

    // Update every 60 seconds to show real-time changes
    const interval = setInterval(fetchAvailability, 60000);

    // Show widget after 20 seconds on page (give user plenty of time to read)
    const showTimer = setTimeout(() => {
      // Only show if not dismissed in this session
      const dismissed = sessionStorage.getItem('availability-widget-dismissed');
      if (!dismissed) {
        setIsVisible(true);
      }
    }, 20000);

    return () => {
      clearInterval(interval);
      clearTimeout(showTimer);
    };
  }, []);

  const handleBookingClick = () => {
    navigate('/booking/intro');
  };

  if (!shouldShowWidget) return null;
  if (!isVisible || isLoading) return null;
  if (!availability) return null;

  const isUrgent = availability.thisWeek <= 2;

  return (
    <AnimatePresence>
      {!isMinimized && (
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.8 }}
          transition={{ type: "spring", stiffness: 260, damping: 20 }}
          className="fixed bottom-4 right-4 z-50 neo-card bg-white p-4 shadow-lg max-w-xs"
          style={{ 
            boxShadow: '4px 4px 0px 0px rgba(0,0,0,1)' 
          }}
        >
          {/* Close button */}
          <button
            onClick={() => {
              setIsMinimized(true);
              // Remember dismissal for this session
              sessionStorage.setItem('availability-widget-dismissed', 'true');
            }}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 text-2xl leading-none p-1"
            aria-label="Luk"
          >
            ×
          </button>


          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-primary" />
              <h3 className="font-bold text-sm">Ledige tider</h3>
            </div>

            {/* Availability stats */}
            <div className="space-y-2">
              <div className="text-sm text-gray-600">
                <span>{availability.thisWeek} {availability.thisWeek === 1 ? 'tid' : 'tider'} ledige denne uge</span>
              </div>

              {availability.nextAvailable && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span>Næste ledige: <strong>{availability.nextAvailable}</strong></span>
                </div>
              )}
            </div>

            {/* Progress bar showing scarcity */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Denne uge</span>
                <span>{availability.thisWeek}/10 ledige</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <motion.div 
                  className="h-full bg-primary"
                  initial={{ width: 0 }}
                  animate={{ width: `${(availability.thisWeek / 10) * 100}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>

            {/* CTA Button */}
            <button
              onClick={handleBookingClick}
              className="w-full neo-button bg-primary hover:bg-primary/90 py-2 text-sm"
            >
              Se ledige tider
            </button>

            {/* Trust signal */}
            <p className="text-xs text-center text-gray-500">
              Helt uforpligtende
            </p>
          </div>
        </motion.div>
      )}

      {/* Minimized state */}
      {isMinimized && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          onClick={() => setIsMinimized(false)}
          className="fixed bottom-4 right-4 z-50 neo-button p-3 bg-primary"
          aria-label="Show availability"
        >
          <Calendar className="w-5 h-5" />
        </motion.button>
      )}
    </AnimatePresence>
  );
};

export default AvailabilityWidget;