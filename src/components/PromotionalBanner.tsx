import React, { useState, useEffect } from 'react';
import { X, Mic } from 'lucide-react';
import { useAnalytics } from '../services/analytics';
import { Link } from 'react-router-dom';

const BANNER_DISMISS_KEY = 'promotional_banner_dismissed';
const BANNER_DISMISS_COUNT_KEY = 'promotional_banner_dismiss_count';
const BANNER_LAST_SHOWN_KEY = 'promotional_banner_last_shown';
const MAX_DISMISSALS = 3;
const WEEK_IN_MS = 7 * 24 * 60 * 60 * 1000;

const PromotionalBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const analytics = useAnalytics();

  useEffect(() => {
    const checkBannerVisibility = () => {
      const dismissCount = parseInt(localStorage.getItem(BANNER_DISMISS_COUNT_KEY) || '0');
      const lastShown = parseInt(localStorage.getItem(BANNER_LAST_SHOWN_KEY) || '0');
      const currentTime = Date.now();
      
      // Reset weekly
      if (currentTime - lastShown > WEEK_IN_MS) {
        localStorage.removeItem(BANNER_DISMISS_KEY);
        localStorage.setItem(BANNER_LAST_SHOWN_KEY, currentTime.toString());
      }
      
      // Check if should show
      const isDismissed = localStorage.getItem(BANNER_DISMISS_KEY) === 'true';
      const shouldShow = !isDismissed && dismissCount < MAX_DISMISSALS;
      
      if (shouldShow) {
        setTimeout(() => {
          setIsVisible(true);
          setIsAnimating(true);
          // Set CSS variable for header positioning
          document.documentElement.style.setProperty('--banner-height', '60px');
        }, 1000); // Delay for better UX
      }
    };

    checkBannerVisibility();
    
    return () => {
      document.documentElement.style.setProperty('--banner-height', '0px');
    };
  }, []);

  const handleDismiss = () => {
    setIsAnimating(false);
    document.documentElement.style.setProperty('--banner-height', '0px');
    setTimeout(() => {
      setIsVisible(false);
      localStorage.setItem(BANNER_DISMISS_KEY, 'true');
      
      // Update dismiss count
      const currentCount = parseInt(localStorage.getItem(BANNER_DISMISS_COUNT_KEY) || '0');
      localStorage.setItem(BANNER_DISMISS_COUNT_KEY, (currentCount + 1).toString());
      
      analytics.track('promotional_banner_dismissed', {
        banner_type: 'free_speaking_2025',
        dismiss_count: currentCount + 1
      });
    }, 300);
  };

  const handleCTAClick = () => {
    analytics.track('promotional_banner_cta_clicked', {
      banner_type: 'free_speaking_2025',
      cta_text: 'Book mig som speaker'
    });
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Spacer to push content down when banner is visible */}
      {isVisible && isAnimating && (
        <div className="h-[60px]" />
      )}
      
      <div 
        className={`fixed top-0 left-0 right-0 z-[60] bg-primary text-black transition-transform duration-300 ease-out ${
          isAnimating ? 'translate-y-0' : '-translate-y-full'
        }`}
      >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-3 gap-4">
          <div className="flex items-center gap-4 flex-1">
            <div className="flex items-center gap-2 bg-white/20 px-3 py-1 rounded-full text-sm font-semibold whitespace-nowrap">
              <Mic className="w-4 h-4" />
              <span>Gratis foredrag resten af 2025! 🎤</span>
            </div>
            
            <p className="hidden md:block text-sm">
              Jeg holder gratis marketing-foredrag på konferencer og events resten af året. 
              Emner: AI i marketing, Growth hacking reality check, eller noget helt tredje.
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Link
              to="/speaking"
              onClick={handleCTAClick}
              className="neo-button bg-white hover:bg-white/90 text-black text-sm px-4 py-2 font-semibold whitespace-nowrap"
            >
              Book mig som speaker →
            </Link>
            
            <button
              onClick={handleDismiss}
              className="p-1 hover:bg-white/20 rounded-full transition-colors"
              aria-label="Luk banner"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default PromotionalBanner;