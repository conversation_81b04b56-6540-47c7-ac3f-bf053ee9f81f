import React, { useState, useEffect } from 'react';
import { Sparkles, Zap, Coffee } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import InteractiveLogo from './InteractiveLogo';
import LanguageSwitcher from './LanguageSwitcher';
import { ctaButton } from '../data/navigationLinks';
import { useAnalytics } from '../services/analytics';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [coffeesConsumed, setCoffeesConsumed] = useState(3);
  const { t } = useTranslation('common');
  const analytics = useAnalytics();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Fun coffee counter that increases during work hours
  useEffect(() => {
    const updateCoffeeCount = () => {
      const hour = new Date().getHours();
      if (hour >= 8 && hour < 17) {
        setCoffeesConsumed(Math.floor((hour - 8) * 0.5) + 2);
      } else if (hour >= 6 && hour < 8) {
        setCoffeesConsumed(1); // Morning coffee
      } else {
        setCoffeesConsumed(0);
      }
    };
    updateCoffeeCount();
    const interval = setInterval(updateCoffeeCount, 3600000); // Update every hour
    return () => clearInterval(interval);
  }, []);

  // Get coffee message based on count
  const getCoffeeMessage = () => {
    if (coffeesConsumed === 0) return "Ingen kaffe endnu 😴";
    if (coffeesConsumed <= 2) return `${coffeesConsumed} kaffe - Lige startet ☕`;
    if (coffeesConsumed <= 5) return `${coffeesConsumed} kaffe - Nu kører det 🚀`;
    return `${coffeesConsumed} kaffe - Måske for meget ⚡`;
  };

  return (
    <header
      className={`fixed left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'h-16 bg-white neo-border border-t-0 border-l-0 border-r-0'
          : 'h-20 bg-transparent'
      }`}
      style={{ top: 'var(--banner-height, 0px)' }}
    >
      <div className="container mx-auto px-4 h-full flex justify-between items-center gap-4">
        <div className="brand flex-shrink-0">
          <InteractiveLogo isScrolled={isScrolled} />
        </div>

        {/* Fun elements in the middle */}
        <div className="hidden md:flex items-center gap-6 text-sm">
          <div className="flex items-center gap-2 px-3 py-1 bg-blue/10 rounded-full">
            <Coffee className="w-4 h-4" />
            <span className="font-medium">{getCoffeeMessage()}</span>
          </div>
          <div className="flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-accent animate-pulse" />
            <span className="hidden xl:inline text-gray-600">Lokal sparringspartner på Fyn</span>
          </div>
        </div>

        <div className="flex items-center gap-3 flex-shrink-0">
          <LanguageSwitcher variant="header" />
          
          {/* Mobile CTA */}
          <a
            href={ctaButton.href}
            onClick={() => analytics.track('cta_clicked', {
              cta_text: t(ctaButton.labelKey),
              cta_location: 'header',
              destination: ctaButton.href,
            })}
            className="md:hidden neo-button bg-primary hover:bg-primary/90 text-sm px-3 py-2"
          >
            <Zap className="w-4 h-4" />
          </a>
          
          {/* Desktop CTA */}
          <a
            href={ctaButton.href}
            onClick={() => analytics.track('cta_clicked', {
              cta_text: t(ctaButton.labelKey),
              cta_location: 'header',
              destination: ctaButton.href,
            })}
            className="hidden md:flex neo-button bg-primary hover:bg-primary/90 text-sm xl:text-base px-4 xl:px-6 items-center gap-2 group"
          >
            <Zap className="w-4 h-4 group-hover:animate-bounce-light" />
            <span className="hidden lg:inline">{t(ctaButton.labelKey)}</span>
            <span className="lg:hidden">Book 2 timer</span>
          </a>
        </div>
      </div>
    </header>
  );
};

export default Header;