import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAnalytics } from '../../services/analytics';

// Map routes to readable page names
const getPageName = (pathname: string): string => {
  const routeMap: Record<string, string> = {
    '/': 'Home',
    '/about': 'About',
    '/experience': 'Experience',
    '/contact': 'Contact',
    '/booking': 'Booking',
    '/services': 'Services',
    '/success': 'Success',
    '/request-success': 'Request Success',
  };

  // Handle dynamic routes like /services/:serviceId
  if (pathname.startsWith('/services/') && pathname !== '/services') {
    return 'Service Detail';
  }

  return routeMap[pathname] || 'Unknown Page';
};

const PageViewTracker = () => {
  const location = useLocation();
  const analytics = useAnalytics();

  useEffect(() => {
    const pageName = getPageName(location.pathname);

    // Only track page view once through our analytics service
    // Remove duplicate PostHog pageview to reduce event volume
    analytics.trackPageView(pageName, {
      search: location.search,
      hash: location.hash,
    });
  }, [location, analytics]);

  return null; // This component doesn't render anything
};

export default PageViewTracker;
