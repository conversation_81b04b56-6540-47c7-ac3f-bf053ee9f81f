
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAnalytics } from '../services/analytics';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useTranslation('common');
  const analytics = useAnalytics();
  
  return (
    <footer className="py-12 neo-border border-b-0 border-l-0 border-r-0">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <h3 className="text-xl font-bold mb-4">Asger.me</h3>
            <p className="mb-4">{t('footer.description')}</p>
            <div className="flex space-x-4">
              <a href="https://linkedin.com/in/asger" className="hover:text-primary transition-colors" target="_blank" rel="noopener noreferrer">LinkedIn</a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">{t('footer.servicesTitle')}</h3>
            <ul className="space-y-2">
              <li><a href="/booking" className="hover:text-primary transition-colors">{t('footer.allServices')}</a></li>
              <li><a href="/speaking" className="hover:text-primary transition-colors">Foredrag</a></li>
              <li><a href="/booking/intro" className="hover:text-primary transition-colors">Gratis intro</a></li>
              <li><a href="/about" className="hover:text-primary transition-colors">{t('footer.about')}</a></li>
              <li><a href="/contact" className="hover:text-primary transition-colors">{t('footer.contact')}</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">{t('footer.getInTouchTitle')}</h3>
            <p className="mb-2">{t('footer.getInTouchDescription')}</p>
            <a
              href="/booking"
              onClick={() => analytics.track('cta_clicked', {
                cta_text: t('footer.bookStrategyCall'),
                cta_location: 'footer',
                destination: '/booking',
              })}
              className="neo-button bg-primary hover:bg-primary/90 text-black text-sm px-4 py-2 inline-block"
            >
              {t('footer.bookStrategyCall')}
            </a>
          </div>
        </div>

        <div className="mt-12 pt-6 border-t-2 border-black">
          <div className="text-center space-y-2">
            <p>{t('footer.copyright')}</p>
            <p className="text-sm text-gray-600">{t('footer.cvrNumber')}</p>
            <div className="mt-4 space-x-4 text-sm">
              <a href="/privacy" className="text-gray-600 hover:text-primary transition-colors">Privatlivspolitik</a>
              <span className="text-gray-400">|</span>
              <a href="/terms" className="text-gray-600 hover:text-primary transition-colors">Handelsbetingelser</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
