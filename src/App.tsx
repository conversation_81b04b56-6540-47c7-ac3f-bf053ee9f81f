
import React, { Suspense, lazy } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { HelmetProvider } from 'react-helmet-async';
import ScrollToTop from "./components/ScrollToTop";
import PageViewTracker from "./components/analytics/PageViewTracker";
import FaviconDebugger from "./components/FaviconDebugger";
import LoadingSpinner from "./components/LoadingSpinner";
import SecurityHeaders from "./components/SecurityHeaders";
import SkipLink from "./components/SkipLink";
import NewsletterSlideIn from "./components/NewsletterSlideIn";
import PromotionalBanner from "./components/PromotionalBanner";
import AvailabilityWidget from "./components/AvailabilityWidget";
import './i18n/config';

// Lazy load pages for better performance
const Index = lazy(() => import("./pages/Index"));
const About = lazy(() => import("./pages/About"));
const Experience = lazy(() => import("./pages/Experience"));
const Contact = lazy(() => import("./pages/Contact"));
const Speaking = lazy(() => import("./pages/Speaking"));
const Booking = lazy(() => import("./pages/SimplifiedBooking"));
const BookingSuccess = lazy(() => import("./pages/BookingSuccess"));
const IntroBooking = lazy(() => import("./pages/IntroBooking"));
const IntroBookingSuccess = lazy(() => import("./pages/IntroBookingSuccess"));
const Services = lazy(() => import("./pages/Services"));
const ServiceSingle = lazy(() => import("./pages/ServiceSingle"));
const MarketingTerapiSession = lazy(() => import("./pages/services/MarketingTerapiSession"));
const Success = lazy(() => import("./pages/Success"));
const RequestSuccess = lazy(() => import("./pages/RequestSuccess"));
const NotFound = lazy(() => import("./pages/NotFound"));
const TestComprehensiveAnalysis = lazy(() => import("./test-comprehensive-analysis"));
const TestFullBookingFlow = lazy(() => import("./test-full-booking-flow"));
const TestRealAPI = lazy(() => import("./test-real-api"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const Terms = lazy(() => import("./pages/Terms"));

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <SecurityHeaders />
          <SkipLink />
          <PromotionalBanner />
          <ScrollToTop />
          <PageViewTracker />
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/about" element={<About />} />
              <Route path="/speaking" element={<Speaking />} />
              <Route path="/experience" element={<Experience />} />
              <Route path="/services" element={<Navigate to="/" replace />} />
              <Route path="/services/:serviceId" element={<Navigate to="/" replace />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/booking" element={<Booking />} />
              <Route path="/booking/intro" element={<IntroBooking />} />
              <Route path="/booking/intro-success" element={<IntroBookingSuccess />} />
              <Route path="/booking/marketing-terapi-session" element={<Booking />} />
              <Route path="/booking/success" element={<BookingSuccess />} />
              {/* Services pages hidden for launch */}
              {/* <Route path="/services" element={<Services />} /> */}
              {/* <Route path="/services/marketing-terapi-session" element={<MarketingTerapiSession />} /> */}
              {/* <Route path="/services/:serviceId" element={<ServiceSingle />} /> */}
              <Route path="/success" element={<Success />} />
              <Route path="/request-success" element={<RequestSuccess />} />
              <Route path="/privacy" element={<PrivacyPolicy />} />
              <Route path="/terms" element={<Terms />} />
              {/* Test routes - remove in production */}
              <Route path="/test-analysis" element={<TestComprehensiveAnalysis />} />
              <Route path="/test-full-flow" element={<TestFullBookingFlow />} />
              <Route path="/test-real-api" element={<TestRealAPI />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
          <NewsletterSlideIn />
          <AvailabilityWidget />
          <FaviconDebugger />
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
