import React, { useState, useEffect } from 'react';
import { ArrowLeft, Clock, Check, Calendar, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { format, addDays, isWeekend, setHours, setMinutes } from 'date-fns';
import { da } from 'date-fns/locale';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { toast } from '../hooks/use-toast';
import { useAnalytics } from '../services/analytics';

const IntroBooking = () => {
  const navigate = useNavigate();
  const analytics = useAnalytics();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    currentChallenge: '',
    preferredTime: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availabilityData, setAvailabilityData] = useState<{
    thisWeek: number;
    nextAvailable: string | null;
  } | null>(null);
  
  const [availableSlots, setAvailableSlots] = useState<Array<{
    id: string;
    date: string;
    time: string;
    available: boolean;
  }>>([]);
  
  const [loadingSlots, setLoadingSlots] = useState(true);
  
  // Fetch real-time availability summary
  useEffect(() => {
    const fetchAvailability = async () => {
      try {
        const apiUrl = '/api/availability-summary';
        
        const response = await fetch(apiUrl);
        if (response.ok) {
          const data = await response.json();
          setAvailabilityData(data);
        }
      } catch (error) {
        console.error('Failed to fetch availability:', error);
        // Use fallback data based on available slots if we have them
        if (availableSlots.length > 0) {
          const availableCount = availableSlots.filter(s => s.available).length;
          const firstAvailable = availableSlots.find(s => s.available);
          setAvailabilityData({
            thisWeek: availableCount,
            nextAvailable: firstAvailable ? 
              `${firstAvailable.date} kl. ${firstAvailable.time}` : 
              null
          });
        } else {
          setAvailabilityData({
            thisWeek: 3,
            nextAvailable: 'i morgen kl. 10:00'
          });
        }
      }
    };
    
    fetchAvailability();
    // Refresh every 30 seconds
    const interval = setInterval(fetchAvailability, 30000);
    return () => clearInterval(interval);
  }, [availableSlots]);
  
  // Fetch actual available slots from API
  useEffect(() => {
    const fetchSlots = async () => {
      setLoadingSlots(true);
      const slots = [];
      let slotId = 1;
      
      // Try to fetch real availability for the next 7 days
      for (let dayOffset = 1; dayOffset <= 7; dayOffset++) {
        const date = addDays(new Date(), dayOffset);
        
        // Skip weekends
        if (isWeekend(date)) continue;
        
        try {
          const dateStr = format(date, 'yyyy-MM-dd');
          const response = await fetch(`/api/available-slots?date=${dateStr}`);
          
          if (response.ok) {
            const data = await response.json();
            const daySlots = data.slots?.slice(0, 2).map((slot: any) => ({
              id: (slotId++).toString(),
              date: format(date, 'EEEE d. MMMM', { locale: da }),
              time: slot.time,
              available: slot.available
            }));
            if (daySlots) slots.push(...daySlots);
          }
        } catch (error) {
          // Fallback: add some mock slots for this day
          const timeSlots = ['09:00', '10:00', '14:00', '15:00'];
          const mockSlots = timeSlots
            .slice(0, 2)
            .map(time => ({
              id: (slotId++).toString(),
              date: format(date, 'EEEE d. MMMM', { locale: da }),
              time,
              available: Math.random() > 0.3
            }));
          slots.push(...mockSlots);
        }
        
        // Stop if we have enough slots
        if (slots.length >= 6) break;
      }
      
      setAvailableSlots(slots.slice(0, 6));
      setLoadingSlots(false);
    };
    
    fetchSlots();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Fornavn er påkrævet';
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Efternavn er påkrævet';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email er påkrævet';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Indtast venligst en gyldig email';
    }
    if (!formData.currentChallenge.trim()) {
      newErrors.currentChallenge = 'Beskriv kort din udfordring';
    }
    if (!formData.preferredTime) {
      newErrors.preferredTime = 'Vælg venligst et tidspunkt';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Track intro booking
      analytics.track('intro_booking_completed', {
        challenge_preview: formData.currentChallenge.substring(0, 50)
      });

      // Find the selected time slot details
      console.log('Looking for slot:', formData.preferredTime);
      console.log('Available slots:', availableSlots.map(s => `${s.date}-${s.time}`));
      
      const selectedSlot = availableSlots.find(s => `${s.date}-${s.time}` === formData.preferredTime);
      if (!selectedSlot) {
        throw new Error('Vælg venligst et tidspunkt');
      }
      
      console.log('Selected slot:', selectedSlot);
      
      // Parse the date for the calendar event
      // We need to convert Danish date format to a proper date
      const now = new Date();
      const currentYear = now.getFullYear();
      
      // Map Danish months to month numbers
      const monthMap: Record<string, number> = {
        'januar': 0, 'februar': 1, 'marts': 2, 'april': 3,
        'maj': 4, 'juni': 5, 'juli': 6, 'august': 7,
        'september': 8, 'oktober': 9, 'november': 10, 'december': 11
      };
      
      // Extract day and month from "torsdag 7. november" format
      const dateMatch = selectedSlot.date.match(/(\d+)\.\s+(\w+)/);
      if (!dateMatch) {
        throw new Error('Invalid date format');
      }
      
      const day = parseInt(dateMatch[1]);
      const monthName = dateMatch[2].toLowerCase();
      const month = monthMap[monthName] ?? now.getMonth();
      
      // Determine the year (could be next year if booking December in January)
      let year = currentYear;
      if (month < now.getMonth() && month <= 1 && now.getMonth() >= 11) {
        year = currentYear + 1;
      }
      
      // Create ISO datetime string
      const sessionDateTime = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')} ${selectedSlot.time}`;
      
      // Create calendar event
      try {
        const calendarResponse = await fetch('/api/create-calendar-event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_INTERNAL_API_KEY || 'internal-api-key'}`
          },
          body: JSON.stringify({
            customerName: `${formData.firstName} ${formData.lastName}`,
            customerEmail: formData.email,
            sessionDateTime,
            sessionGoals: formData.currentChallenge,
            companyName: formData.phone ? `Phone: ${formData.phone}` : undefined,
            marketingChallenges: formData.currentChallenge
          })
        });
        
        if (!calendarResponse.ok) {
          console.error('Failed to create calendar event, but continuing with booking');
        }
      } catch (error) {
        console.error('Calendar event creation failed:', error);
        // Don't block the booking if calendar fails
      }
      
      // Send confirmation email
      try {
        await fetch('/api/send-booking-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: formData.email,
            customerName: `${formData.firstName} ${formData.lastName}`,
            sessionDate: selectedSlot.date,
            sessionTime: selectedSlot.time,
            sessionType: 'intro',
            zoomLink: 'Sendes separat'
          })
        });
      } catch (error) {
        console.error('Email sending failed:', error);
      }
      
      toast({
        title: "Intro-snak booket! ☕",
        description: "Du modtager en bekræftelse på mail om et øjeblik.",
      });

      // Redirect to success page after a short delay
      setTimeout(() => {
        navigate('/booking/intro-success');
      }, 2000);
      
    } catch (error) {
      console.error('Booking error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      toast({
        title: "Noget gik galt",
        description: errorMessage === 'Vælg venligst et tidspunkt' 
          ? errorMessage 
          : `Prøv igen eller send en <NAME_EMAIL> (${errorMessage})`,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => navigate('/')}
              className="text-gray-600 hover:text-black flex items-center gap-2 mb-4"
            >
              <ArrowLeft className="w-4 h-4" />
              Tilbage til forsiden
            </button>
            
            <div className="inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
              <span>Uforpligtende snak</span>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold mb-2">
              Lad os starte med en snak
            </h1>
            <p className="text-gray-600">
              Book en hurtig snak, så vi kan se om jeg kan hjælpe med jeres marketing
            </p>
            
            {/* Availability info */}
            {availabilityData && (
              <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <p className="text-sm text-gray-600">
                  {availabilityData.thisWeek} {availabilityData.thisWeek === 1 ? 'tid' : 'tider'} ledige denne uge
                  {availabilityData.nextAvailable && ` • Næste ledige: ${availabilityData.nextAvailable}`}
                </p>
              </div>
            )}
          </div>

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="neo-card p-4 bg-white">
              <Clock className="w-5 h-5 mb-2 text-primary" />
              <p className="font-semibold text-sm">Hurtig snak</p>
              <p className="text-xs text-gray-600">Helt uforpligtende</p>
            </div>
            <div className="neo-card p-4 bg-white">
              <Check className="w-5 h-5 mb-2 text-primary" />
              <p className="font-semibold text-sm">Gratis</p>
              <p className="text-xs text-gray-600">Ingen forpligtelser</p>
            </div>
            <div className="neo-card p-4 bg-white">
              <Calendar className="w-5 h-5 mb-2 text-primary" />
              <p className="font-semibold text-sm">Næste ledige tid</p>
              <p className="text-xs text-gray-600">
                {availabilityData?.nextAvailable || 'Typisk inden for 2-3 dage'}
              </p>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="neo-card p-6 md:p-8 bg-white">
            <div className="space-y-6">
              {/* Name fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Fornavn *
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`neo-input w-full ${errors.firstName ? 'border-red-500' : ''}`}
                    placeholder="Anders"
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Efternavn *
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`neo-input w-full ${errors.lastName ? 'border-red-500' : ''}`}
                    placeholder="Andersen"
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                  )}
                </div>
              </div>

              {/* Contact fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`neo-input w-full ${errors.email ? 'border-red-500' : ''}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Telefon (valgfrit)
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="neo-input w-full"
                    placeholder="+45 12 34 56 78"
                  />
                </div>
              </div>

              {/* Challenge description */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Hvad vil du gerne snakke om? *
                </label>
                <textarea
                  name="currentChallenge"
                  value={formData.currentChallenge}
                  onChange={handleInputChange}
                  rows={3}
                  className={`neo-input w-full ${errors.currentChallenge ? 'border-red-500' : ''}`}
                  placeholder="Beskriv kort din marketing-udfordring eller hvad du gerne vil have hjælp med..."
                />
                {errors.currentChallenge && (
                  <p className="text-red-500 text-sm mt-1">{errors.currentChallenge}</p>
                )}
                <p className="text-sm text-gray-600 mt-1">
                  Jo mere specifik du er, jo bedre kan jeg forberede mig
                </p>
              </div>

              {/* Time slots */}
              <div>
                <label className="block text-sm font-medium mb-3">
                  Vælg et tidspunkt *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {loadingSlots ? (
                    <>
                      {[1, 2, 3, 4].map(i => (
                        <div key={i} className="neo-card p-3 animate-pulse">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      ))}
                    </>
                  ) : availableSlots.length === 0 ? (
                    <p className="col-span-2 text-gray-600">Ingen ledige tider lige nu. Prøv igen senere.</p>
                  ) : (
                    availableSlots.map(slot => (
                    <label
                      key={slot.id}
                      className={`neo-card p-3 cursor-pointer transition-all ${
                        !slot.available 
                          ? 'opacity-50 cursor-not-allowed' 
                          : formData.preferredTime === `${slot.date}-${slot.time}`
                          ? 'bg-primary/10 border-primary'
                          : 'bg-white hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="radio"
                        name="preferredTime"
                        value={`${slot.date}-${slot.time}`}
                        checked={formData.preferredTime === `${slot.date}-${slot.time}`}
                        onChange={handleInputChange}
                        disabled={!slot.available}
                        className="sr-only"
                      />
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{slot.date}</p>
                          <p className="text-gray-600 text-sm">kl. {slot.time}</p>
                        </div>
                        {!slot.available && (
                          <span className="text-xs text-gray-500">Optaget</span>
                        )}
                        {slot.available && formData.preferredTime === slot.id && (
                          <Check className="w-5 h-5 text-primary" />
                        )}
                      </div>
                    </label>
                  )))}
                </div>
                {errors.preferredTime && (
                  <p className="text-red-500 text-sm mt-2">{errors.preferredTime}</p>
                )}
              </div>

              {/* Submit button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="neo-button bg-primary hover:bg-primary/90 w-full flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    'Booker...'
                  ) : (
                    <>
                      Book gratis intro-snak
                      <Check className="w-4 h-4" />
                    </>
                  )}
                </button>
              </div>

              {/* Info text */}
              <div className="text-center text-sm text-gray-600">
                <p>Efter intro-snakken kan du booke en fuld 2-timers session</p>
                <p className="mt-1">Du forpligter dig ikke til noget</p>
              </div>
            </div>
          </form>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default IntroBooking;