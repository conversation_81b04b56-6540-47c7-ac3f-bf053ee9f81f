
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Hero from '../components/Hero';
import ProblemSection from '../components/ProblemSection';
import SolutionSection from '../components/SolutionSection';
import Testimonials from '../components/Testimonials';
import SimplifiedProcess from '../components/SimplifiedProcess';
import AboutMini from '../components/AboutMini';
import FAQSection from '../components/FAQSection';
import FinalCTA from '../components/FinalCTA';
import Footer from '../components/Footer';

const Index = () => {
  const navigate = useNavigate();

  const handleBookingClick = () => {
    navigate('/booking');
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow">
        <Hero onBookingClick={handleBookingClick} />
        <ProblemSection />
        <SolutionSection onBookingClick={handleBookingClick} />
        <Testimonials />
        <SimplifiedProcess />
        <AboutMini />
        <FAQSection />
        <FinalCTA onBookingClick={handleBookingClick} />
      </main>
      
      <Footer />
    </div>
  );
};

export default Index;
