import React, { useState } from 'react';
import { ArrowLef<PERSON>, <PERSON>R<PERSON>, Check } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { FormProvider } from '../contexts/FormContext';
import SimplifiedStepOne from '../components/booking/SimplifiedStepOne';
import SimplifiedStepTwo from '../components/booking/SimplifiedStepTwo';
import SimplifiedStepThree from '../components/booking/SimplifiedStepThree';
import { useFormContext } from '../contexts/FormContext';
import { Progress } from '../components/ui/progress';
import { toast } from '../hooks/use-toast';
import { useAnalytics } from '../services/analytics';
import Header from '../components/Header';
import Footer from '../components/Footer';

// StepSelector component to render the current step
const StepSelector = () => {
  const { currentStep } = useFormContext();

  switch (currentStep) {
    case 1:
      return <SimplifiedStepOne />;
    case 2:
      return <SimplifiedStepTwo />;
    case 3:
      return <SimplifiedStepThree />;
    default:
      return <SimplifiedStepOne />;
  }
};

// Navigation buttons component
const NavigationButtons = () => {
  const { goToPreviousStep, currentStep, formData, setErrors, setCurrentStep } = useFormContext();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const analytics = useAnalytics();

  // Custom validation for simplified form
  const validateStep = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    if (currentStep === 1) {
      console.log('Validating Step 1 - Current formData:', {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        primaryChallenge: formData.primaryChallenge
      });
      
      if (!formData.firstName?.trim()) {
        newErrors.firstName = 'Fornavn er påkrævet';
        isValid = false;
      }
      if (!formData.lastName?.trim()) {
        newErrors.lastName = 'Efternavn er påkrævet';
        isValid = false;
      }
      if (!formData.email?.trim()) {
        newErrors.email = 'Email er påkrævet';
        isValid = false;
      }
      if (!formData.primaryChallenge) {
        newErrors.primaryChallenge = 'Vælg venligst din primære udfordring';
        isValid = false;
      }
      
      console.log('Validation errors:', newErrors);
    }

    if (currentStep === 2) {
      if (!formData.marketingSetup) {
        newErrors.marketingSetup = 'Vælg venligst jeres setup';
        isValid = false;
      }
      if (!formData.runningAds) {
        newErrors.runningAds = 'Vælg venligst om I kører annoncering';
        isValid = false;
      }
      // Only validate budget if they're running ads
      if (formData.runningAds === 'yes' && !formData.marketingBudget) {
        newErrors.marketingBudget = 'Vælg venligst budget niveau';
        isValid = false;
      }
      if (!formData.website?.trim()) {
        newErrors.website = 'Website er påkrævet';
        isValid = false;
      }
      if (!formData.challengeDescription?.trim()) {
        newErrors.challengeDescription = 'Beskriv venligst jeres udfordring';
        isValid = false;
      }
    }

    if (currentStep === 3) {
      if (!formData.sessionGoals?.trim()) {
        newErrors.sessionGoals = 'Beskriv hvad du håber at få ud af sessionen';
        isValid = false;
      }
      if (!formData.agreedToTerms) {
        newErrors.agreedToTerms = 'Du skal acceptere betingelserne';
        isValid = false;
      }
      if (!formData.understoodPreparation) {
        newErrors.understoodPreparation = 'Du skal acceptere forberedelsen';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (currentStep === 3) {
        handleSubmit();
      } else {
        // Manually go to next step without calling the built-in validation
        setCurrentStep(currentStep + 1);
        window.scrollTo(0, 0);
      }
    }
  };

  const handleSubmit = async () => {
    // For step 3, we don't need to do anything - the actual booking happens via the "Book og betal" button
    // This function is kept for consistency but doesn't perform any action
  };

  return (
    <div className="flex justify-between items-center mt-8">
      {currentStep > 1 ? (
        <button
          onClick={goToPreviousStep}
          className="neo-button bg-white hover:bg-muted flex items-center gap-2"
          disabled={isSubmitting}
        >
          <ArrowLeft className="w-4 h-4" />
          Tilbage
        </button>
      ) : (
        <div />
      )}

      {currentStep !== 3 && (
        <button
          onClick={handleNext}
          disabled={isSubmitting}
          className="neo-button bg-primary hover:bg-primary/90 flex items-center gap-2"
        >
          {isSubmitting ? (
            <>Behandler...</>
          ) : (
            <>
              Næste
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </button>
      )}
    </div>
  );
};

const SimplifiedBooking = () => {
  const TOTAL_STEPS = 3;

  return (
    <FormProvider>
      <SimplifiedBookingContent totalSteps={TOTAL_STEPS} />
    </FormProvider>
  );
};

const SimplifiedBookingContent = ({ totalSteps }: { totalSteps: number }) => {
  const { currentStep } = useFormContext();
  const navigate = useNavigate();

  const stepTitles = [
    'Basis info',
    'Din situation',
    'Book tid'
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => navigate('/')}
              className="text-gray-600 hover:text-black flex items-center gap-2 mb-4"
            >
              <ArrowLeft className="w-4 h-4" />
              Tilbage til forsiden
            </button>
            
            <h1 className="text-3xl md:text-4xl font-bold mb-2">
              Book din 2-timers marketing session
            </h1>
            <p className="text-gray-600">
              Udfyld formularen så jeg kan forberede mig bedst muligt
            </p>
          </div>

          {/* Progress */}
          <div className="mb-8">
            <div className="flex justify-between mb-2">
              {stepTitles.map((title, index) => (
                <div
                  key={index}
                  className={`text-sm ${
                    index + 1 <= currentStep 
                      ? 'text-black font-semibold' 
                      : 'text-gray-400'
                  }`}
                >
                  {index + 1}. {title}
                </div>
              ))}
            </div>
            <Progress value={(currentStep / totalSteps) * 100} className="h-2" />
          </div>

          {/* Form Content */}
          <div className="neo-card p-6 md:p-8 bg-white">
            <StepSelector />
            <NavigationButtons />
          </div>

          {/* Trust indicators */}
          <div className="mt-8 text-center text-sm text-gray-600">
            <p>💯 100% fortrolig behandling af dine oplysninger</p>
            <p className="mt-2">🚀 Næste ledige tid om 3 dage</p>
            <p className="mt-2 text-green-600">✓ Dine oplysninger gemmes automatisk</p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SimplifiedBooking;