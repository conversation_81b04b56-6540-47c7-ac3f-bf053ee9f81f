import React from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowLeft, Mic, Calendar, MapPin, Clock, Check } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAnalytics } from '../services/analytics';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SEOHead from '../components/SEOHead';

const Speaking = () => {
  const { t } = useTranslation(['common', 'home']);
  const navigate = useNavigate();
  const analytics = useAnalytics();

  const topics = [
    {
      title: "AI i Marketing - Reality Check",
      description: "Praktisk guide til hvad der faktisk virker og hvad der er hype",
      duration: "45 min"
    },
    {
      title: "Growth Hacking uden BS",
      description: "10 års erfaring destilleret til konkrete strategier",
      duration: "45 min"
    },
    {
      title: "Fra 0 til 10x - Marketing der skalerer",
      description: "Case studies fra startups jeg har hjulpet med at vokse",
      duration: "30-60 min"
    },
    {
      title: "Marketing Therapy Session",
      description: "Interaktiv workshop hvor vi løser deltagernes udfordringer live",
      duration: "60-90 min"
    }
  ];

  const benefits = [
    "Ingen salgspitch - kun værdifuld viden",
    "Praktiske takeaways deltagerne kan bruge med det samme",
    "Engagerende og underholdende fremfor kedelige slides",
    "Q&A hvor jeg svarer på alt (næsten)",
    "Gratis resten af 2025 - I betaler kun transport"
  ];

  const handleBookingClick = () => {
    analytics.track('speaking_cta_clicked', {
      cta_location: 'speaking_page',
      cta_text: 'Book gratis foredrag'
    });
  };

  return (
    <>
      <SEOHead 
        title="Gratis Marketing Foredrag 2025 | Asger Teglgaard"
        description="Book gratis marketing foredrag til jeres event eller konference. AI i marketing, growth hacking, eller skræddersyet indhold. Kun transport skal dækkes."
      />
      
      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="container mx-auto px-4 py-8 pt-24">
          <div className="max-w-4xl mx-auto">
            {/* Back button */}
            <button
              onClick={() => navigate('/')}
              className="text-gray-600 hover:text-black flex items-center gap-2 mb-8"
            >
              <ArrowLeft className="w-4 h-4" />
              Tilbage til forsiden
            </button>

            {/* Hero Section */}
            <div className="neo-card p-8 md:p-12 bg-primary mb-12">
              <div className="flex items-center gap-3 mb-4">
                <Mic className="w-8 h-8" />
                <span className="neo-badge bg-white text-black">Limited Time Offer</span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Gratis marketing foredrag resten af 2025
              </h1>
              
              <p className="text-xl mb-6">
                Normalt 15-20.000 kr - nu gratis for at teste nye foredragskoncepter. 
                Jeg dækker forberedelse og levering, I dækker kun transport.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="mailto:<EMAIL>?subject=Gratis foredrag 2025"
                  onClick={handleBookingClick}
                  className="neo-button bg-white hover:bg-white/90 text-black inline-flex items-center justify-center gap-2"
                >
                  <Calendar className="w-5 h-5" />
                  Book gratis foredrag
                </a>
                
                <Link
                  to="/contact"
                  className="neo-button bg-transparent border-2 border-black hover:bg-black hover:text-primary inline-flex items-center justify-center"
                >
                  Har du spørgsmål?
                </Link>
              </div>
            </div>

            {/* Topics Section */}
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-8">Populære emner</h2>
              
              <div className="grid gap-6">
                {topics.map((topic, index) => (
                  <div key={index} className="neo-card p-6 bg-white">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-bold">{topic.title}</h3>
                      <span className="text-sm text-gray-600 flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {topic.duration}
                      </span>
                    </div>
                    <p className="text-gray-700">{topic.description}</p>
                  </div>
                ))}
              </div>
              
              <p className="mt-6 text-gray-600">
                <strong>Eller noget helt andet!</strong> Fortæl mig om jeres event og målgruppe, 
                så skræddersyer jeg indholdet.
              </p>
            </section>

            {/* Benefits Section */}
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-8">Hvad I får</h2>
              
              <div className="neo-card p-8 bg-secondary/10">
                <ul className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-lg">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </section>

            {/* Logistics Section */}
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-8">Praktisk info</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="neo-card p-6 bg-white">
                  <div className="flex items-center gap-2 mb-3">
                    <MapPin className="w-5 h-5 text-primary" />
                    <h3 className="font-bold">Lokation</h3>
                  </div>
                  <p>Hele Danmark - jeg kommer gerne til jer. Baseret på Fyn, så Odense-området er ekstra nemt.</p>
                </div>
                
                <div className="neo-card p-6 bg-white">
                  <div className="flex items-center gap-2 mb-3">
                    <Calendar className="w-5 h-5 text-primary" />
                    <h3 className="font-bold">Booking</h3>
                  </div>
                  <p>Minimum 2 ugers varsel. Tilbuddet gælder resten af 2025 eller til kalenderen er fyldt.</p>
                </div>
              </div>
            </section>

            {/* CTA Section */}
            <section className="neo-card p-8 md:p-12 bg-accent text-center">
              <h2 className="text-3xl font-bold mb-4">Klar til at booke?</h2>
              <p className="text-xl mb-8">
                Send en mail med info om jeres event, så finder vi en dato der passer.
              </p>
              
              <a
                href="mailto:<EMAIL>?subject=Gratis foredrag 2025&body=Hej Asger,%0D%0A%0D%0AVi er interesserede i et gratis foredrag.%0D%0A%0D%0AEvent: [Navn på event]%0D%0AForventet antal deltagere: [Antal]%0D%0ALokation: [By/område]%0D%0AMulige datoer: [Forslag]%0D%0AØnsket emne/fokus: [Beskriv kort]%0D%0A%0D%0AMvh"
                onClick={handleBookingClick}
                className="neo-button bg-white hover:bg-white/90 text-black inline-flex items-center gap-2 text-lg px-8 py-3"
              >
                <Mic className="w-5 h-5" />
                Book gratis foredrag nu
              </a>
              
              <p className="mt-6 text-sm">
                Eller ring på +45 93 93 77 96 hvis det haster
              </p>
            </section>
          </div>
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default Speaking;