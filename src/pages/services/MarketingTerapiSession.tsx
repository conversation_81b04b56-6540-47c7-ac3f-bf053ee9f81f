import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Target, 
  Clock, 
  CheckCircle2, 
  Calendar,
  MessageSquare,
  FileText,
  Users,
  Zap,
  ArrowRight,
  Coffee
} from 'lucide-react';
import { getServiceById } from '../../data/services';
import SEOHead from '../../components/SEOHead';

const MarketingTerapiSession: React.FC = () => {
  const navigate = useNavigate();
  const service = getServiceById('marketing-strategy-consultation');

  if (!service) {
    navigate('/services');
    return null;
  }

  const handleBooking = () => {
    navigate('/booking/marketing-terapi-session');
  };

  return (
    <>
      <SEOHead 
        title="Marketing Terapi Session - 60 minutters marketing terapi"
        description="Få konkret hjælp til dine marketing udfordringer. 60 minutters session med Asger - ingen BS, bare løsninger der virker."
        keywords="marketing terapi, marketing konsultation, marketing strategi, marketing rådgivning"
      />

      <div className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-primary/5 border-b-4 border-black">
          <div className="container mx-auto px-4 py-16 md:py-24">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-flex items-center gap-2 neo-badge mb-6">
                  <Target className="w-4 h-4" />
                  <span className="text-sm font-medium">Marketing Terapi</span>
                </div>
                
                <h1 className="text-4xl md:text-6xl font-black mb-6 leading-tight">
                  Marketing Terapi Session
                </h1>
                
                <p className="text-xl md:text-2xl mb-8 text-muted-foreground">
                  {service.shortDescription}
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  <button
                    onClick={handleBooking}
                    className="neo-button bg-primary hover:bg-primary/90 text-primary-foreground flex items-center justify-center gap-2 text-lg px-8 py-4"
                  >
                    Book din session nu
                    <ArrowRight className="w-5 h-5" />
                  </button>
                  
                  <div className="neo-card p-4 bg-white">
                    <p className="text-2xl font-bold">kr. {service.price.toLocaleString('da-DK')}</p>
                    <p className="text-sm text-muted-foreground">{service.priceNote}</p>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-primary" />
                    <span>{service.deliveryTime}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-primary" />
                    <span>Book med 24 timers varsel</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4 text-primary" />
                    <span>Via Zoom</span>
                  </div>
                </div>
              </div>
              
              <div className="relative">
                <div className="neo-card p-8 bg-white rotate-2">
                  <img 
                    src="/<EMAIL>" 
                    alt="Asger" 
                    className="w-full h-auto rounded-lg"
                  />
                  <div className="mt-4 text-center">
                    <p className="font-bold">Asger Teglgaard</p>
                    <p className="text-sm text-muted-foreground">10+ års marketing erfaring</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* What's Included */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-black mb-12 text-center">
              Hvad får du med?
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {service.includes.map((item, index) => (
                <div key={index} className="neo-card p-6 hover:-rotate-1 transition-transform">
                  <CheckCircle2 className="w-8 h-8 text-primary mb-4" />
                  <p className="font-medium">{item}</p>
                </div>
              ))}
            </div>
            
            <div className="neo-card p-8 bg-primary/10 max-w-3xl mx-auto">
              <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                <Coffee className="w-6 h-6" />
                Hvordan det fungerer
              </h3>
              <p className="text-lg mb-4">
                {service.fullDescription}
              </p>
              <p className="text-muted-foreground">
                Ingen PowerPoint præsentationer, ingen fancy buzzwords - bare ærlig snak om 
                hvad der virker for din virksomhed.
              </p>
            </div>
          </div>
        </section>

        {/* Process */}
        <section className="py-16 md:py-24 bg-muted/50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-black mb-12 text-center">
              Sådan foregår det
            </h2>
            
            <div className="max-w-4xl mx-auto">
              {service.process.map((step, index) => (
                <div key={index} className="flex gap-4 mb-8">
                  <div className="neo-button w-12 h-12 flex items-center justify-center flex-shrink-0 bg-primary text-primary-foreground">
                    {index + 1}
                  </div>
                  <div className="neo-card p-6 flex-grow">
                    <p className="font-medium">{step}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-black mb-12 text-center">
              Derfor virker det
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {service.features.map((feature, index) => (
                <div key={index} className="flex gap-4">
                  <Zap className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                  <p className="text-lg">{feature}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonial */}
        {service.testimonial && (
          <section className="py-16 md:py-24 bg-muted/50">
            <div className="container mx-auto px-4">
              <div className="neo-card p-8 md:p-12 max-w-3xl mx-auto bg-white -rotate-1">
                <blockquote className="text-xl md:text-2xl font-medium mb-6">
                  "{service.testimonial.quote}"
                </blockquote>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-bold">{service.testimonial.author}</p>
                    <p className="text-sm text-muted-foreground">{service.testimonial.company}</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* FAQ */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-black mb-12 text-center">
              Ofte stillede spørgsmål
            </h2>
            
            <div className="max-w-3xl mx-auto space-y-6">
              <div className="neo-card p-6">
                <h3 className="font-bold mb-2">Hvad hvis jeg ikke ved præcis hvad mit problem er?</h3>
                <p className="text-muted-foreground">
                  Det er helt normalt! Mange kommer til mig med en følelse af at "noget ikke fungerer". 
                  Vi starter med at snakke om din situation, og så finder vi ud af det sammen.
                </p>
              </div>
              
              <div className="neo-card p-6">
                <h3 className="font-bold mb-2">Skal jeg forberede noget?</h3>
                <p className="text-muted-foreground">
                  Du udfylder en formular når du booker, så jeg kan forberede mig. Derudover er det 
                  smart at have adgang til dine analytics og evt. kampagne data, men det er ikke et krav.
                </p>
              </div>
              
              <div className="neo-card p-6">
                <h3 className="font-bold mb-2">Får jeg en optagelse af sessionen?</h3>
                <p className="text-muted-foreground">
                  Nej, men du får en skriftlig opsummering med alle de vigtige pointer og en konkret 
                  action plan. Så kan du fokusere på vores snak i stedet for at tage noter.
                </p>
              </div>
              
              <div className="neo-card p-6">
                <h3 className="font-bold mb-2">Hvad hvis jeg har brug for mere hjælp efterfølgende?</h3>
                <p className="text-muted-foreground">
                  Du får en opfølgning efter 2 uger, hvor vi kan snakke om eventuelle spørgsmål. 
                  Hvis du har brug for mere hjælp, kan vi altid booke en ny session eller snakke om 
                  et længere forløb.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 md:py-24 bg-primary/5 border-t-4 border-black">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-black mb-6">
              Klar til at få styr på din marketing?
            </h2>
            
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Book din Marketing Terapi Session nu og få konkrete løsninger på dine udfordringer. 
              Ingen binding, ingen BS - bare 60 minutter der rykker.
            </p>
            
            <button
              onClick={handleBooking}
              className="neo-button bg-primary hover:bg-primary/90 text-primary-foreground text-lg px-8 py-4 inline-flex items-center gap-2"
            >
              Book din session nu
              <ArrowRight className="w-5 h-5" />
            </button>
            
            <div className="mt-8 flex flex-wrap justify-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-primary" />
                <span>Næste ledige tid i morgen</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-primary" />
                <span>100% pengene tilbage garanti</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-primary" />
                <span>Ingen binding</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default MarketingTerapiSession;