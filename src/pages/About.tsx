import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Award, Users, TrendingUp, Target } from 'lucide-react';

const About = () => {
  const { t } = useTranslation('about');

  const achievements = [
    {
      icon: <Users size={40} />,
      number: t('achievements.items.0.number'),
      label: t('achievements.items.0.label'),
      description: t('achievements.items.0.description')
    },
    {
      icon: <TrendingUp size={40} />,
      number: t('achievements.items.1.number'),
      label: t('achievements.items.1.label'),
      description: t('achievements.items.1.description')
    },
    {
      icon: <Award size={40} />,
      number: t('achievements.items.2.number'),
      label: t('achievements.items.2.label'),
      description: t('achievements.items.2.description')
    },
    {
      icon: <Target size={40} />,
      number: t('achievements.items.3.number'),
      label: t('achievements.items.3.label'),
      description: t('achievements.items.3.description')
    }
  ];

  const skills = [
    "Growth & B2B SaaS",
    "Performance Marketing",
    "Content & SEO",
    "Web Development",
    "AI/ChatGPT Tools",
    "Marketing Automation",
    "Data-driven Marketing",
    "Undervisning & Workshops"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <section className="py-20 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                  {t('hero.badge')}
                </span>
                <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6">
                  {t('hero.title').split(' ').slice(0, -2).join(' ')} <span className="relative inline-block">
                    {t('hero.title').split(' ').slice(-2).join(' ')}
                    <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                      <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                    </svg>
                  </span>
                </h1>
                <p className="text-xl mb-8">
                  {t('hero.description')}
                </p>
                <a href="/booking" className="neo-button bg-primary hover:bg-primary/90 text-black text-lg">
                  {t('hero.cta')}
                </a>
              </div>
              
              <div className="relative">
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-yellow neo-border rotate-12 animate-float"></div>
                <div className="absolute top-20 -right-5 w-24 h-24 bg-pink neo-border -rotate-6 animate-float [animation-delay:1s]"></div>
                <div className="neo-card overflow-hidden bg-white p-6 rotate-3 animate-fade-in">
                  <img 
                    src="/nerd.jpeg" 
                    alt="Asger Teglgaard" 
                    className="w-full h-full object-cover rounded"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Achievements Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
                {t('achievements.badge')}
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                {t('achievements.title')}
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {achievements.map((achievement, index) => (
                <div 
                  key={index}
                  className="neo-card p-6 bg-white text-center hover-lift"
                  style={{ 
                    transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
                  }}
                >
                  <div className="mb-4 text-primary flex justify-center">{achievement.icon}</div>
                  <div className="text-3xl font-bold mb-2">{achievement.number}</div>
                  <h3 className="text-lg font-bold mb-2">{achievement.label}</h3>
                  <p className="text-sm text-gray-600">{achievement.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Skills Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-pink text-white font-bold mb-4 -rotate-1">
                Hvad jeg nørder med
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                Marketing cirkusset (hele lortet)
              </h2>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              {skills.map((skill, index) => (
                <div 
                  key={index}
                  className="neo-card p-4 bg-white text-center hover-tilt"
                  style={{ 
                    transform: `rotate(${(index % 3 - 1) * 2}deg)`
                  }}
                >
                  <span className="font-bold">{skill}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 neo-border bg-primary text-black font-bold mb-4 rotate-1">
                  {t('story.badge')}
                </span>
                <h2 className="text-3xl md:text-5xl font-bold">
                  {t('story.title')}
                </h2>
              </div>
              
              <div className="neo-card p-8 bg-white -rotate-1">
                <div className="prose prose-lg max-w-none">
                  {t('story.content', { returnObjects: true }).map((paragraph: string, index: number) => (
                    <p key={index} className="text-lg mb-6 last:mb-0" dangerouslySetInnerHTML={{ __html: paragraph }} />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Framework Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 -rotate-1">
                  {t('framework.badge')}
                </span>
                <h2 className="text-3xl md:text-5xl font-bold mb-2">
                  {t('framework.title')}
                </h2>
                <p className="text-lg text-gray-600">
                  {t('framework.subtitle')}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                {t('framework.phases', { returnObjects: true }).map((phase: any, index: number) => (
                  <div 
                    key={index}
                    className="neo-card p-6 bg-white"
                    style={{ transform: `rotate(${index === 1 ? '-1' : index === 2 ? '1' : '0'}deg)` }}
                  >
                    <h3 className="text-2xl font-bold mb-2">{phase.name}</h3>
                    <p className="text-sm text-gray-500 mb-3">{phase.duration}</p>
                    <p>{phase.description}</p>
                  </div>
                ))}
              </div>

              <div className="neo-card p-8 bg-accent rotate-1">
                <h3 className="text-xl font-bold mb-4 text-white">{t('framework.example.title')}</h3>
                <ul className="space-y-3">
                  {t('framework.example.timeline', { returnObjects: true }).map((item: string, index: number) => (
                    <li key={index} className="flex items-start text-white">
                      <span className="font-bold mr-2">→</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Philosophy Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-blue text-white font-bold mb-4 rotate-1">
                {t('philosophy.badge')}
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                {t('philosophy.title')}
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {t('philosophy.principles', { returnObjects: true }).map((principle: any, index: number) => (
                <div 
                  key={index}
                  className="neo-card p-6 bg-white hover-lift"
                  style={{ transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)` }}
                >
                  <h3 className="text-xl font-bold mb-2">{principle.title}</h3>
                  <p className="text-gray-600">{principle.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default About;
