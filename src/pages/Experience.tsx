import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Users } from 'lucide-react';

const Experience = () => {
  const experiences = [
    {
      company: "KeyShot (Digizuite)",
      title: "Sr. Growth Marketing Manager",
      period: "jan 2024 - now",
      tags: ["DAM", "B2B", "SaaS", "Enterprise", "EMEA", "NA"],
      logo: "/experience-logos/kslogo.png",
      logoColor: "#0066CC",
      description: "Leading strategic demand generation post-acquisition, implementing ABM strategies and enhancing go-to-market effectiveness."
    },
    {
      company: "Opus Group",
      title: "Head of Marketing (LearnX)",
      period: "may 2023 - jan 2024",
      tags: ["Events", "B2B", "Talent", "Agency", "EMEA"],
      logo: "/experience-logos/opuslogo.png",
      logoColor: "#9C27B0",
      description: "Led comprehensive B2B demand generation strategy, managing cross-functional teams and optimizing pipeline growth."
    },
    {
      company: "KUBO Robotics",
      title: "Head of Marketing (LearnX)",
      period: "may 2021 - april 2023",
      tags: ["Edtech", "B2B", "SaaS", "Startup", "EMEA", "NA"],
      logo: "/experience-logos/kubologo.png",
      logoColor: "#4A90E2",
      description: "Building a subscription business model from scratch. Creating a growth strategy and consolidating a small internal team. Taking the brand to the United States. Rebuilding a digital client platform with a small team of developers."
    },
    {
      company: "QuickOrder",
      title: "Head of Marketing",
      period: "oct 2019 - apr 2021",
      tags: ["Hospitality", "B2B", "SaaS", "Startup", "Series A", "EMEA"],
      logo: "/experience-logos/quickorderlogo.png",
      logoColor: "#E53E3E",
      description: "Drove explosive growth through strategic demand generation and sales enablement initiatives."
    },
    {
      company: "CoWeb",
      title: "Founder and CEO",
      period: "jan 2015 - nov 2018",
      tags: ["Marketing", "B2B", "Agency", "EMEA"],
      logo: "/experience-logos/coweblogo.png",
      logoColor: "#00A3FF",
      description: "Founded and scaled a marketing agency, delivering 150+ B2B marketing projects."
    }
  ];

  const certifications = [
    "Google Analytics Certified",
    "Google Ads Certified",
    "HubSpot Marketing Certified",
    "Certified Growth Hacker",
    "Mentor at Pavilion"
  ];

  const speakingEngagements = [
    {
      event: "Marketing Summit 2024",
      topic: "The Future of B2B Marketing",
      audience: "200+ Marketing Professionals"
    },
    {
      event: "Startup Growth Conference",
      topic: "Scaling Marketing in Early-Stage Companies",
      audience: "150+ Founders & CMOs"
    },
    {
      event: "Digital Marketing Meetup",
      topic: "Data-Driven Marketing Strategies",
      audience: "100+ Digital Marketers"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <section className="py-20 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                Professional Journey
              </span>
              <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6">
                10+ Years of Marketing <span className="relative inline-block">
                  Excellence
                  <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                    <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                  </svg>
                </span>
              </h1>
              <p className="text-xl max-w-3xl mx-auto">
                From marketing coordinator to strategic consultant, my journey has been driven by 
                a passion for helping businesses grow through innovative marketing strategies.
              </p>
            </div>
          </div>
        </section>

        {/* Experience Timeline */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
                Career Timeline
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                Professional Experience
              </h2>
            </div>
            
            <div className="max-w-4xl mx-auto">
              {experiences.map((exp, index) => (
                <div key={index} className="relative mb-8 last:mb-0">
                  {/* Timeline line */}
                  {index < experiences.length - 1 && (
                    <div className="absolute left-8 top-24 w-1 h-16 bg-black hidden md:block"></div>
                  )}

                  <div className="neo-card p-6 bg-white hover-lift" style={{
                    transform: `rotate(${index % 2 === 0 ? '0.5' : '-0.5'}deg)`
                  }}>
                    <div className="flex items-start gap-6">
                      {/* Company Logo */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 neo-border bg-white flex items-center justify-center p-2">
                          <img
                            src={exp.logo}
                            alt={`${exp.company} logo`}
                            className="w-full h-full object-contain"
                          />
                        </div>
                      </div>

                      <div className="flex-grow">
                        {/* Tags */}
                        <div className="flex flex-wrap gap-2 mb-3">
                          {exp.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className={`px-3 py-1 text-sm font-medium neo-border ${
                                tag === 'EMEA' || tag === 'NA' ? 'bg-blue text-white' : 'bg-white text-black'
                              }`}
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Company and Period */}
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-3">
                          <h3 className="text-2xl font-bold">{exp.company}</h3>
                          <span className="text-sm text-gray-600 font-medium">{exp.period}</span>
                        </div>

                        {/* Title */}
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">{exp.title}</h4>

                        {/* Description */}
                        <p className="text-gray-600">{exp.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Certifications & Speaking */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Certifications */}
              <div>
                <div className="text-center mb-8">
                  <span className="inline-block px-4 py-2 neo-border bg-pink text-white font-bold mb-4 -rotate-1">
                    Credentials
                  </span>
                  <h2 className="text-2xl md:text-3xl font-bold">
                    Certifications
                  </h2>
                </div>
                
                <div className="space-y-4">
                  {certifications.map((cert, index) => (
                    <div 
                      key={index}
                      className="neo-card p-4 bg-white hover-tilt"
                      style={{ 
                        transform: `rotate(${(index % 3 - 1) * 1}deg)`
                      }}
                    >
                      <span className="font-semibold">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Speaking Engagements */}
              <div>
                <div className="text-center mb-8">
                  <span className="inline-block px-4 py-2 neo-border bg-primary text-black font-bold mb-4 rotate-1">
                    Speaking
                  </span>
                  <h2 className="text-2xl md:text-3xl font-bold">
                    Recent Engagements
                  </h2>
                </div>
                
                <div className="space-y-6">
                  {speakingEngagements.map((speaking, index) => (
                    <div 
                      key={index}
                      className="neo-card p-6 bg-white hover-lift"
                      style={{ 
                        transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
                      }}
                    >
                      <h3 className="font-bold text-lg mb-2">{speaking.event}</h3>
                      <p className="text-primary font-semibold mb-2">{speaking.topic}</p>
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Users className="w-4 h-4" />
                        <span>{speaking.audience}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Experience;
