import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { 
  Loader2, CheckCircle, ArrowRight, ArrowLeft, Building, Globe, Mail, Phone, Sparkles, 
  Search, Check, X, Info, AlertCircle, RefreshCw, Edit2, Save, FileText, TrendingUp,
  Users, Calendar, Target, DollarSign, BarChart, Lightbulb, Brain, Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { mockAnalyzeWebsiteComprehensive } from './services/mockAnalysisService';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';

// Form data interface matching actual booking form
interface FormData {
  // Step 1
  firstName: string;
  lastName: string;
  email: string;
  website: string;
  companyName: string;
  
  // Step 2
  businessType: string;
  businessStage: string;
  industry: string;
  otherIndustry: string;
  
  // Step 3
  runningAds: string;
  adsEffective: string;
  adBudget: string;
  marketingManagement: string;
  decisionMakers: string[];
  implementationTimeline: string;
  marketingAreas: string[];
  
  // Step 4
  marketingChallenges: string;
  sessionOutcomes: string;
  materialsToShare: string[];
  additionalInfo: string;
  
  // Step 5 / AI
  aiSummary: string;
  websiteAnalysis: any;
  executiveSummary: string;
  additionalWebsiteData: any;
}

// Constants from the actual form
const INDUSTRIES = [
  { value: 'ecommerce', label: 'E-commerce' },
  { value: 'saas', label: 'SaaS' },
  { value: 'finance', label: 'Finance' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'education', label: 'Education' },
  { value: 'travel', label: 'Travel' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'nonprofit', label: 'Non-profit' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'food_beverage', label: 'Food & Beverage' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'technology', label: 'Technology' },
  { value: 'professional_services', label: 'Professional Services' },
  { value: 'other', label: 'Other' }
];

const MARKETING_AREAS = [
  { value: 'paid-advertising', label: 'Paid advertising' },
  { value: 'growth-strategy', label: 'Growth strategy' },
  { value: 'content-marketing', label: 'Content marketing' },
  { value: 'seo', label: 'Search engine optimization' },
  { value: 'email-marketing', label: 'Email marketing' },
  { value: 'offline-marketing', label: 'Offline marketing' },
  { value: 'web-design', label: 'Web design' },
  { value: 'web-development', label: 'Web development' },
  { value: 'web-copy', label: 'Web copy' },
  { value: 'creative-production', label: 'Creative production' },
  { value: 'cro', label: 'CRO/conversion optimization' },
  { value: 'copywriting', label: 'Copywriting' },
  { value: 'social-media', label: 'Social media management' },
  { value: 'influencer-marketing', label: 'Influencer marketing' },
  { value: 'brand-strategy', label: 'Brand strategy' },
  { value: 'crm-analytics', label: 'CRM analytics' },
  { value: 'product-design', label: 'Product design' },
  { value: 'app-marketing', label: 'App marketing' },
  { value: 'marketing-automation', label: 'Marketing automation' },
  { value: 'personal-branding', label: 'Personal branding' },
];

const DECISION_MAKERS = [
  { id: 'just_me', label: 'Kun mig' },
  { id: 'me_and_team', label: 'Mig og mit team' },
  { id: 'other_team', label: 'Andre i teamet' },
];

const MATERIALS = [
  { id: 'analytics', label: 'Analytics adgang' },
  { id: 'campaign_data', label: 'Tidligere kampagnedata' },
  { id: 'brand_guidelines', label: 'Brand guidelines' },
  { id: 'marketing_strategy', label: 'Marketingstrategi' },
];

const TestFullBookingFlow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    // Step 1
    firstName: '',
    lastName: '',
    email: '',
    website: '',
    companyName: '',
    
    // Step 2
    businessType: '',
    businessStage: '',
    industry: '',
    otherIndustry: '',
    
    // Step 3
    runningAds: '',
    adsEffective: '',
    adBudget: '',
    marketingManagement: '',
    decisionMakers: [],
    implementationTimeline: '',
    marketingAreas: [],
    
    // Step 4
    marketingChallenges: '',
    sessionOutcomes: '',
    materialsToShare: [],
    additionalInfo: '',
    
    // Step 5 / AI
    aiSummary: '',
    websiteAnalysis: null,
    executiveSummary: '',
    additionalWebsiteData: null,
  });

  // Analysis state
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [showAnalysisDetails, setShowAnalysisDetails] = useState(false);
  const [isGeneratingFinalSummary, setIsGeneratingFinalSummary] = useState(false);
  const [industryPopoverOpen, setIndustryPopoverOpen] = useState(false);
  const [selectedMarketingAreas, setSelectedMarketingAreas] = useState<string[]>([]);
  const [marketingAreasPopoverOpen, setMarketingAreasPopoverOpen] = useState(false);

  // Validation errors
  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});

  // Improvement suggestions
  const [showImprovements, setShowImprovements] = useState(true);

  // TipTap editor for AI summary
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'AI-genereret resumé vil blive vist her...',
      }),
    ],
    content: formData.aiSummary,
    onUpdate: ({ editor }) => {
      setFormData(prev => ({ ...prev, aiSummary: editor.getHTML() }));
    },
  });

  // Progress calculation
  const calculateProgress = () => {
    const totalFields = 20;
    let filledFields = 0;
    
    if (formData.firstName) filledFields++;
    if (formData.lastName) filledFields++;
    if (formData.email) filledFields++;
    if (formData.website) filledFields++;
    if (formData.companyName) filledFields++;
    if (formData.businessType) filledFields++;
    if (formData.industry) filledFields++;
    if (formData.runningAds) filledFields++;
    if (formData.marketingManagement) filledFields++;
    if (formData.decisionMakers.length > 0) filledFields++;
    if (formData.implementationTimeline) filledFields++;
    if (formData.marketingAreas.length > 0) filledFields++;
    if (formData.marketingChallenges) filledFields++;
    if (formData.sessionOutcomes) filledFields++;
    
    return Math.round((filledFields / totalFields) * 100);
  };

  // Validate current step
  const validateStep = (step: number): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};

    switch (step) {
      case 1:
        if (!formData.firstName) newErrors.firstName = 'Fornavn er påkrævet';
        if (!formData.lastName) newErrors.lastName = 'Efternavn er påkrævet';
        if (!formData.email) newErrors.email = 'Email er påkrævet';
        if (!formData.website) newErrors.website = 'Website er påkrævet';
        if (!formData.companyName) newErrors.companyName = 'Virksomhedsnavn er påkrævet';
        break;
      case 2:
        if (!formData.businessType) newErrors.businessType = 'Vælg venligst en type';
        if (!formData.industry) newErrors.industry = 'Vælg venligst en branche';
        break;
      case 3:
        if (!formData.runningAds) newErrors.runningAds = 'Dette felt er påkrævet';
        if (!formData.marketingManagement) newErrors.marketingManagement = 'Dette felt er påkrævet';
        if (formData.decisionMakers.length === 0) newErrors.decisionMakers = 'Vælg mindst én';
        if (!formData.implementationTimeline) newErrors.implementationTimeline = 'Dette felt er påkrævet';
        if (formData.marketingAreas.length === 0) newErrors.marketingAreas = 'Vælg mindst ét område';
        break;
      case 4:
        if (!formData.marketingChallenges) newErrors.marketingChallenges = 'Dette felt er påkrævet';
        if (!formData.sessionOutcomes) newErrors.sessionOutcomes = 'Dette felt er påkrævet';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle website analysis
  const handleWebsiteAnalysis = async () => {
    if (!formData.website) {
      toast.error('Please enter a website URL first');
      return;
    }

    setIsAnalyzing(true);
    try {
      const url = formData.website.startsWith('http') 
        ? formData.website 
        : `https://${formData.website}`;

      const results = await mockAnalyzeWebsiteComprehensive(url);
      setAnalysisResults(results);

      if (results.success) {
        const analysis = results.analysis;
        setFormData(prev => ({
          ...prev,
          companyName: analysis.company_profile.name || prev.companyName,
          industry: analysis.business_model.industry === 'Restaurant Technology' ? 'technology' : prev.industry,
          websiteAnalysis: analysis,
          executiveSummary: results.executiveSummary,
          additionalWebsiteData: results.additionalData,
        }));

        toast.success('Website analyzed successfully! Form fields have been auto-filled.');
      }
    } catch (error) {
      toast.error('Failed to analyze website');
      console.error(error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate comprehensive AI summary
  const generateComprehensiveSummary = async () => {
    setIsGeneratingFinalSummary(true);
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    const summary = `
      <h3>Marketing Terapi Forberedelse - ${formData.companyName}</h3>
      
      <h4>Virksomhedsprofil</h4>
      <p><strong>${formData.companyName}</strong> er en ${getBusinessTypeLabel(formData.businessType)} inden for ${getIndustryLabel(formData.industry)}. 
      ${analysisResults ? `De betjener primært ${analysisResults.analysis.business_model.customer_segments.join(', ')} på ${analysisResults.analysis.business_model.target_markets.join(', ')} markederne.` : ''}</p>
      
      <h4>Nuværende Marketing Situation</h4>
      <p>Virksomheden ${formData.runningAds === 'yes' ? `kører annoncekampagner med et budget på ${getAdBudgetLabel(formData.adBudget)}` : 'kører ikke annoncekampagner i øjeblikket'}. 
      Marketing håndteres ${getMarketingManagementLabel(formData.marketingManagement)}.</p>
      
      ${analysisResults ? `
      <p>Deres primære værdiforslag er: <em>"${analysisResults.analysis.marketing_insights.value_proposition}"</em></p>
      <p>De bruger følgende kanaler: ${analysisResults.analysis.marketing_insights.marketing_channels.join(', ')}.</p>
      ` : ''}
      
      <h4>Identificerede Udfordringer</h4>
      <p>${formData.marketingChallenges}</p>
      ${analysisResults ? `
      <p><strong>AI-identificerede udfordringer:</strong></p>
      <ul>
        ${analysisResults.analysis.pain_points_opportunities.potential_challenges.map(challenge => `<li>${challenge}</li>`).join('')}
      </ul>
      ` : ''}
      
      <h4>Mål for Marketing Terapi</h4>
      <p>${formData.sessionOutcomes}</p>
      
      <h4>Fokusområder</h4>
      <p>Klienten ønsker hjælp med: ${formData.marketingAreas.map(area => MARKETING_AREAS.find(ma => ma.value === area)?.label).join(', ')}.</p>
      
      ${analysisResults ? `
      <h4>Vækstmuligheder (AI-identificeret)</h4>
      <ul>
        ${analysisResults.analysis.pain_points_opportunities.growth_opportunities.map(opp => `<li>${opp}</li>`).join('')}
      </ul>
      ` : ''}
      
      <h4>Beslutningstagere</h4>
      <p>${formData.decisionMakers.map(dm => DECISION_MAKERS.find(d => d.id === dm)?.label).join(', ')}</p>
      
      <h4>Implementeringstidslinje</h4>
      <p>${getTimelineLabel(formData.implementationTimeline)}</p>
      
      ${formData.materialsToShare.length > 0 ? `
      <h4>Materiale at gennemgå</h4>
      <p>Klienten kan dele: ${formData.materialsToShare.map(m => MATERIALS.find(mat => mat.id === m)?.label).join(', ')}</p>
      ` : ''}
      
      ${formData.additionalInfo ? `
      <h4>Yderligere information</h4>
      <p>${formData.additionalInfo}</p>
      ` : ''}
      
      <h4>Kontaktinformation</h4>
      <p>
        <strong>Navn:</strong> ${formData.firstName} ${formData.lastName}<br/>
        <strong>Email:</strong> ${formData.email}<br/>
        <strong>Website:</strong> ${formData.website}
      </p>
    `;

    editor?.commands.setContent(summary);
    setFormData(prev => ({ ...prev, aiSummary: summary }));
    setIsGeneratingFinalSummary(false);
  };

  // Helper functions
  const getBusinessTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'startup': 'Startup',
      'established_business': 'Etableret virksomhed',
      'agency': 'Bureau',
      'freelancer': 'Freelancer/Konsulent',
      'other': 'Andet'
    };
    return labels[type] || type;
  };

  const getIndustryLabel = (industry: string) => {
    const found = INDUSTRIES.find(i => i.value === industry);
    return found ? found.label : industry;
  };

  const getAdBudgetLabel = (budget: string) => {
    const labels: Record<string, string> = {
      'less_than_5k': 'Under 40k kr/md',
      '5k_10k': '40k-80k kr/md',
      '10k_20k': '80k-160k kr/md',
      '20k_plus': '160k+ kr/md'
    };
    return labels[budget] || budget;
  };

  const getMarketingManagementLabel = (management: string) => {
    const labels: Record<string, string> = {
      'in_house': 'internt',
      'agency': 'af bureau/freelancer',
      'mix': 'som en kombination',
      'not_managed': 'ikke aktivt'
    };
    return labels[management] || management;
  };

  const getTimelineLabel = (timeline: string) => {
    const labels: Record<string, string> = {
      'right_now': 'Har brug for hjælp nu',
      '1_3_months': 'Inden for 1-3 måneder',
      'exploring': 'Undersøger muligheder'
    };
    return labels[timeline] || timeline;
  };

  // Navigation
  const nextStep = () => {
    if (validateStep(currentStep)) {
      if (currentStep === 4) {
        generateComprehensiveSummary();
      }
      setCurrentStep(prev => Math.min(prev + 1, 5));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field as keyof FormData].includes(value)
        ? (prev[field as keyof FormData] as string[]).filter(v => v !== value)
        : [...(prev[field as keyof FormData] as string[]), value]
    }));
  };

  // Handle marketing areas selection
  const handleMarketingAreaToggle = (area: string) => {
    if (selectedMarketingAreas.includes(area)) {
      setSelectedMarketingAreas(prev => prev.filter(a => a !== area));
      setFormData(prev => ({
        ...prev,
        marketingAreas: prev.marketingAreas.filter(a => a !== area)
      }));
    } else if (selectedMarketingAreas.length < 3) {
      setSelectedMarketingAreas(prev => [...prev, area]);
      setFormData(prev => ({
        ...prev,
        marketingAreas: [...prev.marketingAreas, area]
      }));
    } else {
      toast.error('Du kan maksimalt vælge 3 områder');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-2">Test: Complete AI-Enhanced Booking Flow</h1>
          <p className="text-muted-foreground">
            Full booking form with all fields and AI features
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Form Progress</span>
            <span className="text-sm text-muted-foreground">{calculateProgress()}% complete</span>
          </div>
          <Progress value={calculateProgress()} className="h-2" />
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Form Section */}
          <div className="lg:col-span-2">
            {/* Step Indicator */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[1, 2, 3, 4, 5].map((step) => (
                  <div
                    key={step}
                    className={`flex items-center ${step < 5 ? 'flex-1' : ''}`}
                  >
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all ${
                        currentStep >= step
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-muted-foreground'
                      }`}
                    >
                      {currentStep > step ? <CheckCircle className="w-5 h-5" /> : step}
                    </div>
                    {step < 5 && (
                      <div
                        className={`flex-1 h-1 mx-2 transition-all ${
                          currentStep > step ? 'bg-primary' : 'bg-muted'
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between mt-2 text-xs">
                <span>Kontakt</span>
                <span>Virksomhed</span>
                <span>Marketing</span>
                <span>Udfordringer</span>
                <span>Opsummering</span>
              </div>
            </div>

            {/* Form Card */}
            <Card className="shadow-xl">
              <CardContent className="p-6">
                {/* Step 1: Contact Information */}
                {currentStep === 1 && (
                  <div className="space-y-6 animate-fade-in">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">60 minutters Marketing Terapi</h2>
                      <p className="text-muted-foreground mb-6">
                        Lad os starte med det grundlæggende
                      </p>
                    </div>

                    {/* Name fields */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">Fornavn *</Label>
                        <Input
                          id="firstName"
                          value={formData.firstName}
                          onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                          placeholder="Dit fornavn"
                          className={errors.firstName ? 'border-red-500' : ''}
                        />
                        {errors.firstName && (
                          <p className="mt-1 text-red-500 text-sm">{errors.firstName}</p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor="lastName">Efternavn *</Label>
                        <Input
                          id="lastName"
                          value={formData.lastName}
                          onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                          placeholder="Dit efternavn"
                          className={errors.lastName ? 'border-red-500' : ''}
                        />
                        {errors.lastName && (
                          <p className="mt-1 text-red-500 text-sm">{errors.lastName}</p>
                        )}
                      </div>
                    </div>

                    {/* Email */}
                    <div>
                      <Label htmlFor="email">E-mail *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                        className={errors.email ? 'border-red-500' : ''}
                      />
                      {errors.email && (
                        <p className="mt-1 text-red-500 text-sm">{errors.email}</p>
                      )}
                    </div>

                    {/* Website with AI Analysis */}
                    <div>
                      <Label htmlFor="website" className="flex items-center gap-2">
                        Hjemmeside *
                        <Sparkles className="w-4 h-4 text-primary" />
                        <span className="text-xs text-muted-foreground">AI analyserer din hjemmeside</span>
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="website"
                          value={formData.website}
                          onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                          placeholder="www.dinvirksomhed.dk"
                          className={`flex-1 ${errors.website ? 'border-red-500' : ''}`}
                        />
                        <Button
                          onClick={handleWebsiteAnalysis}
                          disabled={isAnalyzing || !formData.website}
                          variant="secondary"
                        >
                          {isAnalyzing ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Analyserer...
                            </>
                          ) : (
                            <>
                              <Globe className="mr-2 h-4 w-4" />
                              Analyser
                            </>
                          )}
                        </Button>
                      </div>
                      {errors.website && (
                        <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
                      )}
                      {!formData.website && (
                        <p className="mt-1.5 text-xs text-muted-foreground">
                          Vi analyserer din hjemmeside for at spare dig tid
                        </p>
                      )}
                    </div>

                    {/* Company Name */}
                    <div>
                      <Label htmlFor="companyName">
                        Virksomhedsnavn *
                        {analysisResults && (
                          <Badge variant="secondary" className="ml-2">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Auto-udfyldt
                          </Badge>
                        )}
                      </Label>
                      <Input
                        id="companyName"
                        value={formData.companyName}
                        onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                        placeholder="Navn på din virksomhed"
                        className={errors.companyName ? 'border-red-500' : ''}
                      />
                      {errors.companyName && (
                        <p className="mt-1 text-red-500 text-sm">{errors.companyName}</p>
                      )}
                    </div>

                    {/* Analysis Results */}
                    {analysisResults && (
                      <Alert className="border-primary/20 bg-primary/5">
                        <CheckCircle className="h-4 w-4 text-primary" />
                        <AlertTitle>Website analyzed successfully!</AlertTitle>
                        <AlertDescription>
                          <div className="mt-2 space-y-2">
                            <p className="text-sm">
                              Found: {analysisResults.analysis.company_profile.name} - 
                              {' '}{analysisResults.analysis.business_model.type} in {analysisResults.analysis.business_model.industry}
                            </p>
                            <Button
                              variant="link"
                              size="sm"
                              onClick={() => setShowAnalysisDetails(!showAnalysisDetails)}
                              className="p-0 h-auto"
                            >
                              {showAnalysisDetails ? 'Hide' : 'Show'} detailed analysis
                            </Button>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}

                {/* Step 2: Business Details */}
                {currentStep === 2 && (
                  <div className="space-y-6 animate-fade-in">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">Fortæl os om din virksomhed</h2>
                      <p className="text-muted-foreground mb-6">
                        Dette hjælper os med at skræddersy vores rådgivning til din situation
                      </p>
                    </div>

                    {/* Business Type */}
                    <div>
                      <Label className="block mb-3">Hvilken type virksomhed er du? *</Label>
                      <RadioGroup
                        value={formData.businessType}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, businessType: value }))}
                        className="grid grid-cols-2 gap-3"
                      >
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="startup" id="startup" />
                          <Label htmlFor="startup" className="cursor-pointer">Startup</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="established_business" id="established" />
                          <Label htmlFor="established" className="cursor-pointer">Etableret virksomhed</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="agency" id="agency" />
                          <Label htmlFor="agency" className="cursor-pointer">Bureau</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="freelancer" id="freelancer" />
                          <Label htmlFor="freelancer" className="cursor-pointer">Freelancer</Label>
                        </div>
                      </RadioGroup>
                      {errors.businessType && (
                        <p className="mt-1 text-red-500 text-sm">{errors.businessType}</p>
                      )}
                    </div>

                    {/* Startup Stage (conditional) */}
                    {formData.businessType === 'startup' && (
                      <Alert className="border-primary/20 bg-primary/5">
                        <Lightbulb className="h-4 w-4 text-primary" />
                        <AlertTitle>Startup-specific questions</AlertTitle>
                        <AlertDescription>
                          <RadioGroup
                            value={formData.businessStage}
                            onValueChange={(value) => setFormData(prev => ({ ...prev, businessStage: value }))}
                            className="mt-3 space-y-2"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="early_stage" id="early" />
                              <Label htmlFor="early" className="text-sm">Tidlig fase (pre-revenue/MVP)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="growth_stage" id="growth" />
                              <Label htmlFor="growth" className="text-sm">Vækstfase (har kunder)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="scale_stage" id="scale" />
                              <Label htmlFor="scale" className="text-sm">Skalerbar fase (etableret marked)</Label>
                            </div>
                          </RadioGroup>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Industry Selector */}
                    <div>
                      <Label>
                        Hvilken branche er I i? *
                        {analysisResults && formData.industry && (
                          <Badge variant="secondary" className="ml-2">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Auto-valgt
                          </Badge>
                        )}
                      </Label>
                      <Popover open={industryPopoverOpen} onOpenChange={setIndustryPopoverOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={industryPopoverOpen}
                            className="w-full justify-between"
                          >
                            {formData.industry ? (
                              <span>{getIndustryLabel(formData.industry)}</span>
                            ) : (
                              <span className="text-muted-foreground">Vælg eller søg efter din branche</span>
                            )}
                            <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" align="start">
                          <Command>
                            <CommandInput placeholder="Søg branche..." />
                            <CommandList>
                              <CommandEmpty>Ingen branche fundet</CommandEmpty>
                              <CommandGroup>
                                {INDUSTRIES.map(industry => (
                                  <CommandItem
                                    key={industry.value}
                                    value={industry.value}
                                    onSelect={() => {
                                      setFormData(prev => ({ ...prev, industry: industry.value }));
                                      setIndustryPopoverOpen(false);
                                    }}
                                  >
                                    <Check
                                      className={`mr-2 h-4 w-4 ${
                                        formData.industry === industry.value ? 'opacity-100' : 'opacity-0'
                                      }`}
                                    />
                                    {industry.label}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      {errors.industry && (
                        <p className="mt-1 text-red-500 text-sm">{errors.industry}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Step 3: Marketing Situation */}
                {currentStep === 3 && (
                  <div className="space-y-6 animate-fade-in">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">Lad os forstå din marketingsituation</h2>
                      <p className="text-muted-foreground mb-6">
                        Fortæl os hvor I står, og hvad I har brug for hjælp til
                      </p>
                    </div>

                    {/* Running Ads */}
                    <div>
                      <Label className="block mb-3">Kører I annoncekampagner i øjeblikket? *</Label>
                      <RadioGroup
                        value={formData.runningAds}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, runningAds: value }))}
                        className="grid grid-cols-2 gap-3"
                      >
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="yes" id="ads_yes" />
                          <Label htmlFor="ads_yes" className="cursor-pointer">Ja</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="no" id="ads_no" />
                          <Label htmlFor="ads_no" className="cursor-pointer">Nej</Label>
                        </div>
                      </RadioGroup>
                      {errors.runningAds && (
                        <p className="mt-1 text-red-500 text-sm">{errors.runningAds}</p>
                      )}
                    </div>

                    {/* Ads Details (conditional) */}
                    {formData.runningAds === 'yes' && (
                      <Alert className="border-primary/20 bg-primary/5">
                        <DollarSign className="h-4 w-4 text-primary" />
                        <AlertTitle>Fortæl os mere om jeres annoncer</AlertTitle>
                        <AlertDescription>
                          <div className="mt-4 space-y-4">
                            {/* Ads Effective */}
                            <div>
                              <Label className="block mb-2">Er de effektive/profitable?</Label>
                              <RadioGroup
                                value={formData.adsEffective}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, adsEffective: value }))}
                                className="grid grid-cols-3 gap-2"
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="yes" id="effective_yes" />
                                  <Label htmlFor="effective_yes" className="text-sm">Ja</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="no" id="effective_no" />
                                  <Label htmlFor="effective_no" className="text-sm">Nej</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="not_sure" id="effective_unsure" />
                                  <Label htmlFor="effective_unsure" className="text-sm">Ved ikke</Label>
                                </div>
                              </RadioGroup>
                            </div>

                            {/* Ad Budget */}
                            <div>
                              <Label className="block mb-2">Hvad er jeres månedlige annoncebudget?</Label>
                              <RadioGroup
                                value={formData.adBudget}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, adBudget: value }))}
                                className="grid grid-cols-2 gap-2"
                              >
                                <div className="flex items-center space-x-2 p-2">
                                  <RadioGroupItem value="less_than_5k" id="budget_1" />
                                  <Label htmlFor="budget_1" className="text-sm">Under 40k kr/md</Label>
                                </div>
                                <div className="flex items-center space-x-2 p-2">
                                  <RadioGroupItem value="5k_10k" id="budget_2" />
                                  <Label htmlFor="budget_2" className="text-sm">40k-80k kr/md</Label>
                                </div>
                                <div className="flex items-center space-x-2 p-2">
                                  <RadioGroupItem value="10k_20k" id="budget_3" />
                                  <Label htmlFor="budget_3" className="text-sm">80k-160k kr/md</Label>
                                </div>
                                <div className="flex items-center space-x-2 p-2">
                                  <RadioGroupItem value="20k_plus" id="budget_4" />
                                  <Label htmlFor="budget_4" className="text-sm">160k+ kr/md</Label>
                                </div>
                              </RadioGroup>
                            </div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Marketing Management */}
                    <div>
                      <Label className="block mb-3">Hvordan håndterer I marketing i dag? *</Label>
                      <RadioGroup
                        value={formData.marketingManagement}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, marketingManagement: value }))}
                        className="grid grid-cols-2 gap-3"
                      >
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="in_house" id="mgmt_1" />
                          <Label htmlFor="mgmt_1" className="cursor-pointer">Internt</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="agency" id="mgmt_2" />
                          <Label htmlFor="mgmt_2" className="cursor-pointer">Bureau/freelancer</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="mix" id="mgmt_3" />
                          <Label htmlFor="mgmt_3" className="cursor-pointer">Kombination</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="not_managed" id="mgmt_4" />
                          <Label htmlFor="mgmt_4" className="cursor-pointer">Ikke pt.</Label>
                        </div>
                      </RadioGroup>
                      {errors.marketingManagement && (
                        <p className="mt-1 text-red-500 text-sm">{errors.marketingManagement}</p>
                      )}
                    </div>

                    {/* Decision Makers */}
                    <div>
                      <Label className="block mb-3">Hvem er involveret i beslutningsprocessen? *</Label>
                      <div className="space-y-2">
                        {DECISION_MAKERS.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors">
                            <Checkbox
                              id={option.id}
                              checked={formData.decisionMakers.includes(option.id)}
                              onCheckedChange={() => handleCheckboxChange('decisionMakers', option.id)}
                            />
                            <Label htmlFor={option.id} className="cursor-pointer flex-1">{option.label}</Label>
                          </div>
                        ))}
                      </div>
                      {errors.decisionMakers && (
                        <p className="mt-1 text-red-500 text-sm">{errors.decisionMakers}</p>
                      )}
                    </div>

                    {/* Timeline */}
                    <div>
                      <Label className="block mb-3">Hvornår skal ændringerne implementeres? *</Label>
                      <RadioGroup
                        value={formData.implementationTimeline}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, implementationTimeline: value }))}
                        className="space-y-2"
                      >
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="right_now" id="timeline_1" />
                          <Label htmlFor="timeline_1" className="cursor-pointer">Jeg har brug for hjælp nu</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="1_3_months" id="timeline_2" />
                          <Label htmlFor="timeline_2" className="cursor-pointer">Inden for 1-3 måneder</Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer">
                          <RadioGroupItem value="exploring" id="timeline_3" />
                          <Label htmlFor="timeline_3" className="cursor-pointer">Undersøger muligheder</Label>
                        </div>
                      </RadioGroup>
                      {errors.implementationTimeline && (
                        <p className="mt-1 text-red-500 text-sm">{errors.implementationTimeline}</p>
                      )}
                    </div>

                    {/* Marketing Areas */}
                    <div>
                      <Label className="block mb-3">
                        Hvilke områder har I mest brug for hjælp til? * 
                        <span className="text-xs text-muted-foreground ml-2">(Vælg op til 3)</span>
                      </Label>
                      <Popover open={marketingAreasPopoverOpen} onOpenChange={setMarketingAreasPopoverOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-between text-left h-auto py-3"
                          >
                            {selectedMarketingAreas.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {selectedMarketingAreas.map(area => (
                                  <Badge key={area} variant="secondary" className="text-xs">
                                    {MARKETING_AREAS.find(ma => ma.value === area)?.label}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">Vælg marketingområder...</span>
                            )}
                            <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" align="start">
                          <Command>
                            <CommandInput placeholder="Søg områder..." />
                            <CommandList>
                              <CommandEmpty>Ingen områder fundet</CommandEmpty>
                              <CommandGroup>
                                {MARKETING_AREAS.map(area => (
                                  <CommandItem
                                    key={area.value}
                                    value={area.value}
                                    onSelect={() => handleMarketingAreaToggle(area.value)}
                                  >
                                    <Check
                                      className={`mr-2 h-4 w-4 ${
                                        selectedMarketingAreas.includes(area.value) ? 'opacity-100' : 'opacity-0'
                                      }`}
                                    />
                                    {area.label}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      {errors.marketingAreas && (
                        <p className="mt-1 text-red-500 text-sm">{errors.marketingAreas}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Step 4: Challenges & Goals */}
                {currentStep === 4 && (
                  <div className="space-y-6 animate-fade-in">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">Få mest ud af din Marketing Terapi</h2>
                      <p className="text-muted-foreground mb-6">
                        Fortæl os hvad der holder dig vågen om natten
                      </p>
                    </div>

                    {/* Marketing Challenges */}
                    <div>
                      <Label htmlFor="challenges">Hvad er dine 3 største marketing-udfordringer? *</Label>
                      <Textarea
                        id="challenges"
                        value={formData.marketingChallenges}
                        onChange={(e) => setFormData(prev => ({ ...prev, marketingChallenges: e.target.value }))}
                        placeholder="F.eks. høj CAC, lave konverteringsrater, uklar positionering..."
                        rows={3}
                        className={errors.marketingChallenges ? 'border-red-500' : ''}
                      />
                      {errors.marketingChallenges && (
                        <p className="mt-1 text-red-500 text-sm">{errors.marketingChallenges}</p>
                      )}
                      {analysisResults && (
                        <p className="mt-2 text-xs text-muted-foreground">
                          <Info className="inline w-3 h-3 mr-1" />
                          AI identificerede udfordringer: {analysisResults.analysis.pain_points_opportunities.potential_challenges.slice(0, 2).join(', ')}...
                        </p>
                      )}
                    </div>

                    {/* Session Outcomes */}
                    <div>
                      <Label htmlFor="outcomes">Hvad vil du gerne opnå med denne session? *</Label>
                      <Textarea
                        id="outcomes"
                        value={formData.sessionOutcomes}
                        onChange={(e) => setFormData(prev => ({ ...prev, sessionOutcomes: e.target.value }))}
                        placeholder="F.eks. bedre annoncestrategi, højere konverteringsrate på landingssider..."
                        rows={3}
                        className={errors.sessionOutcomes ? 'border-red-500' : ''}
                      />
                      {errors.sessionOutcomes && (
                        <p className="mt-1 text-red-500 text-sm">{errors.sessionOutcomes}</p>
                      )}
                      {analysisResults && (
                        <p className="mt-2 text-xs text-muted-foreground">
                          <Info className="inline w-3 h-3 mr-1" />
                          AI foreslår fokus på: {analysisResults.analysis.pain_points_opportunities.growth_opportunities.slice(0, 2).join(', ')}...
                        </p>
                      )}
                    </div>

                    {/* Materials to Share */}
                    <div>
                      <Label className="block mb-3">Har du materiale vi kan kigge på inden mødet? (valgfrit)</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {MATERIALS.map((option) => (
                          <div key={option.id} className="flex items-center space-x-2 border rounded-lg p-2.5 hover:bg-muted/50 transition-colors">
                            <Checkbox
                              id={option.id}
                              checked={formData.materialsToShare.includes(option.id)}
                              onCheckedChange={() => handleCheckboxChange('materialsToShare', option.id)}
                            />
                            <Label htmlFor={option.id} className="cursor-pointer text-sm flex-1">{option.label}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div>
                      <Label htmlFor="additional">Andet vi bør vide? (valgfrit)</Label>
                      <Textarea
                        id="additional"
                        value={formData.additionalInfo}
                        onChange={(e) => setFormData(prev => ({ ...prev, additionalInfo: e.target.value }))}
                        placeholder="Yderligere kontekst eller spørgsmål..."
                        rows={2}
                      />
                    </div>
                  </div>
                )}

                {/* Step 5: AI Summary & Review */}
                {currentStep === 5 && (
                  <div className="space-y-6 animate-fade-in">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">Bekræft og tilpas</h2>
                      <p className="text-muted-foreground mb-6">
                        Jeg har lavet et summary baseret på alle dine svar
                      </p>
                    </div>

                    {/* AI Summary Section */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-bold flex items-center gap-2">
                          <Sparkles className="text-primary" />
                          AI Summary til Asger
                        </h3>
                        <Button
                          onClick={generateComprehensiveSummary}
                          variant="outline"
                          size="sm"
                          disabled={isGeneratingFinalSummary}
                        >
                          <RefreshCw className={`h-4 w-4 mr-2 ${isGeneratingFinalSummary ? 'animate-spin' : ''}`} />
                          Generer igen
                        </Button>
                      </div>

                      {isGeneratingFinalSummary ? (
                        <Card className="p-8 bg-primary/5">
                          <div className="flex flex-col items-center justify-center">
                            <Loader2 className="animate-spin h-8 w-8 text-primary mb-4" />
                            <p className="text-center text-muted-foreground">
                              Genererer omfattende summary baseret på dine svar...
                            </p>
                          </div>
                        </Card>
                      ) : (
                        <Card className="p-6 bg-primary/5">
                          <EditorContent
                            editor={editor}
                            className="prose prose-sm max-w-none min-h-[400px]"
                          />
                        </Card>
                      )}

                      <Alert className="mt-4">
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          Dette summary sendes til Asger så han kan forberede sig optimalt til jeres session.
                          Du kan redigere direkte i tekstfeltet ovenfor.
                        </AlertDescription>
                      </Alert>
                    </div>

                    {/* Next Steps */}
                    <Card className="p-6 bg-secondary/10">
                      <h3 className="font-bold mb-3 flex items-center gap-2">
                        <CheckCircle className="text-secondary" />
                        Hvad sker der nu?
                      </h3>
                      <ol className="space-y-2 text-sm">
                        <li className="flex gap-2">
                          <span className="font-bold text-secondary">1.</span>
                          <span>Du bliver videresendt til sikker betaling via Stripe (kr. 1.500 + moms)</span>
                        </li>
                        <li className="flex gap-2">
                          <span className="font-bold text-secondary">2.</span>
                          <span>Efter betaling vælger du en tid i kalenderen der passer dig</span>
                        </li>
                        <li className="flex gap-2">
                          <span className="font-bold text-secondary">3.</span>
                          <span>Du modtager en bekræftelse med Zoom link og forberedelsestips</span>
                        </li>
                        <li className="flex gap-2">
                          <span className="font-bold text-secondary">4.</span>
                          <span>Vi ses til din Marketing Terapi Session!</span>
                        </li>
                      </ol>
                    </Card>
                  </div>
                )}

                {/* Navigation */}
                <div className="flex justify-between mt-8">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Tilbage
                  </Button>
                  
                  {currentStep < 5 ? (
                    <Button onClick={nextStep}>
                      Næste
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      onClick={() => {
                        toast.success('Form submitted successfully! (This is a test - no data was actually sent)');
                        console.log('Final form data:', formData);
                      }}
                      className="bg-primary"
                    >
                      Fortsæt til betaling
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Improvements Panel */}
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-primary" />
                    AI Improvement Suggestions
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowImprovements(!showImprovements)}
                  >
                    {showImprovements ? <X className="h-4 w-4" /> : <Lightbulb className="h-4 w-4" />}
                  </Button>
                </CardTitle>
              </CardHeader>
              {showImprovements && (
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      Quick Wins
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Add phone number field for better lead quality</li>
                      <li>• Show live preview of AI summary while typing</li>
                      <li>• Add progress save to prevent data loss</li>
                      <li>• Implement keyboard shortcuts (Tab/Enter)</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm flex items-center gap-2">
                      <Brain className="h-4 w-4 text-purple-500" />
                      AI Enhancements
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Suggest marketing areas based on industry</li>
                      <li>• Auto-detect budget range from website</li>
                      <li>• Predict session outcomes from challenges</li>
                      <li>• Generate custom tips based on business type</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      Conversion Boosters
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Add testimonials between steps</li>
                      <li>• Show "X spots left this week" urgency</li>
                      <li>• Display money-back guarantee</li>
                      <li>• Add exit-intent popup with discount</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-sm flex items-center gap-2">
                      <Users className="h-4 w-4 text-blue-500" />
                      UX Improvements
                    </h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Reduce form to 3 steps maximum</li>
                      <li>• Add skip option for optional fields</li>
                      <li>• Implement smart defaults</li>
                      <li>• Show time estimate (5-7 minutes)</li>
                    </ul>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Debug Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Debug Information</CardTitle>
                <CardDescription>Current form state</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="formdata" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="formdata">Form Data</TabsTrigger>
                    <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
                  </TabsList>
                  <TabsContent value="formdata">
                    <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-64">
                      {JSON.stringify(formData, null, 2)}
                    </pre>
                  </TabsContent>
                  <TabsContent value="analysis">
                    <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-64">
                      {JSON.stringify(analysisResults, null, 2)}
                    </pre>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Analysis Details (shown when expanded) */}
            {showAnalysisDetails && analysisResults && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Website Analysis Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="profile" className="w-full">
                    <TabsList className="grid grid-cols-2 mb-4">
                      <TabsTrigger value="profile">Company</TabsTrigger>
                      <TabsTrigger value="marketing">Marketing</TabsTrigger>
                    </TabsList>
                    <TabsContent value="profile" className="space-y-2">
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Company Profile</h4>
                        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(analysisResults.analysis.company_profile, null, 2)}
                        </pre>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Business Model</h4>
                        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(analysisResults.analysis.business_model, null, 2)}
                        </pre>
                      </div>
                    </TabsContent>
                    <TabsContent value="marketing" className="space-y-2">
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Marketing Insights</h4>
                        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(analysisResults.analysis.marketing_insights, null, 2)}
                        </pre>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm mb-1">Growth Opportunities</h4>
                        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(analysisResults.analysis.pain_points_opportunities, null, 2)}
                        </pre>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestFullBookingFlow;