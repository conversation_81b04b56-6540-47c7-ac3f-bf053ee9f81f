import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import AppWithAnalytics from './components/AppWithAnalytics.tsx'
import './index.css'
import { dynamicFaviconService } from './services/dynamicFaviconService'
// import { initWebVitals, initPerformanceObserver } from './utils/webVitals'
import { initErrorTracking } from './utils/errorTracking'

// PostHog configuration
// Disable PostHog completely in development to avoid rate limiting issues
const isLocalDev = import.meta.env.DEV;
const posthogKey = isLocalDev ? null : import.meta.env.VITE_PUBLIC_POSTHOG_KEY;
const posthogHost = import.meta.env.VITE_PUBLIC_POSTHOG_HOST;

// Debug logging for development
if (import.meta.env.DEV) {
  console.log('PostHog disabled in local development');
}

const options = {
  api_host: posthogHost || 'https://eu.i.posthog.com',
  person_profiles: 'identified_only' as const,
  // Always disable in development
  disabled: isLocalDev || !posthogKey,
  capture_pageview: false, // We handle this manually
  capture_pageleave: false, // Disable to reduce events
  // Disable autocapture to prevent excessive events
  autocapture: false,
  // Reduce session recording
  session_recording: {
    maskAllInputs: true,
    maskAllText: true,
    recordCrossOriginIframes: false,
  },
  // Batch configuration for fewer API calls
  batch_size: 50, // Larger batches
  batch_flush_interval_ms: 10000, // Send every 10 seconds
  // Disable some automatic features
  capture_performance: false,
  disable_session_recording: true, // Always disable session recording
  loaded: (posthog: unknown) => {
    // No debug in dev since we're disabling it
  },
}

// Start dynamic favicon service
dynamicFaviconService.start();

// Initialize monitoring
initErrorTracking();
// Disable web vitals and performance observer to reduce PostHog events
// initWebVitals();
// initPerformanceObserver();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AppWithAnalytics posthogKey={posthogKey} options={options} />
  </StrictMode>
);
