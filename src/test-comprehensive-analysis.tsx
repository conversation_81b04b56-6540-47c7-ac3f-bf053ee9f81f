import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, ArrowRight, Building, Globe, Mail, Phone, Sparkles } from 'lucide-react';
import { toast } from 'sonner';
import { mockAnalyzeWebsiteComprehensive } from './services/mockAnalysisService';
import { Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { useEditor, EditorContent } from '@tiptap/react';

const TestComprehensiveAnalysis = () => {
  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    websiteUrl: '',
    companyName: '',
    industry: '',
    challenges: '',
    goals: '',
    aiSummary: ''
  });

  // Analysis state
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [showAnalysisDetails, setShowAnalysisDetails] = useState(false);

  // TipTap editor for AI summary
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'AI-genereret resumé vil blive vist her...',
      }),
    ],
    content: formData.aiSummary,
    onUpdate: ({ editor }) => {
      setFormData(prev => ({ ...prev, aiSummary: editor.getHTML() }));
    },
  });

  // Step 1: Basic Information
  const handleWebsiteAnalysis = async () => {
    if (!formData.websiteUrl) {
      toast.error('Please enter a website URL first');
      return;
    }

    setIsAnalyzing(true);
    try {
      // Add https:// if not present
      const url = formData.websiteUrl.startsWith('http') 
        ? formData.websiteUrl 
        : `https://${formData.websiteUrl}`;

      const results = await mockAnalyzeWebsiteComprehensive(url);
      setAnalysisResults(results);

      // Auto-fill form fields based on analysis
      if (results.success) {
        const analysis = results.analysis;
        setFormData(prev => ({
          ...prev,
          companyName: analysis.company_profile.name || prev.companyName,
          industry: analysis.business_model.industry || prev.industry,
        }));

        toast.success('Website analyzed successfully! Form fields have been auto-filled.');
      }
    } catch (error) {
      toast.error('Failed to analyze website');
      console.error(error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate AI summary based on all collected data
  const generateAISummary = () => {
    if (!analysisResults) return;

    const summary = `
      <h3>Marketing Terapi Forberedelse for ${formData.companyName}</h3>
      
      <p><strong>Virksomhedsprofil:</strong><br/>
      ${analysisResults.analysis.company_profile.name} er en ${analysisResults.analysis.business_model.type} virksomhed inden for ${analysisResults.analysis.business_model.industry}. 
      De betjener primært ${analysisResults.analysis.business_model.customer_segments.join(', ')} på ${analysisResults.analysis.business_model.target_markets.join(', ')} markederne.</p>
      
      <p><strong>Nuværende Marketing Situation:</strong><br/>
      Deres primære værdiforslag er: "${analysisResults.analysis.marketing_insights.value_proposition}". 
      De bruger følgende kanaler: ${analysisResults.analysis.marketing_insights.marketing_channels.join(', ')}.</p>
      
      <p><strong>Identificerede Udfordringer:</strong><br/>
      ${formData.challenges || analysisResults.analysis.pain_points_opportunities.potential_challenges.join(', ')}</p>
      
      <p><strong>Mål for Marketing Terapi:</strong><br/>
      ${formData.goals || 'At styrke marketingstrategien og øge væksten'}</p>
      
      <p><strong>Fokusområder for Sessionen:</strong></p>
      <ul>
        ${analysisResults.analysis.pain_points_opportunities.growth_opportunities.map(opp => `<li>${opp}</li>`).join('')}
      </ul>
      
      <p><strong>Kontaktinformation:</strong><br/>
      Navn: ${formData.name}<br/>
      Email: ${formData.email}<br/>
      Telefon: ${formData.phone}</p>
    `;

    editor?.commands.setContent(summary);
    setFormData(prev => ({ ...prev, aiSummary: summary }));
  };

  const nextStep = () => {
    if (currentStep === 2) {
      generateAISummary();
    }
    setCurrentStep(prev => Math.min(prev + 1, 3));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-2">Test: AI-Enhanced Booking Flow</h1>
          <p className="text-muted-foreground">
            Experience the complete booking form with all AI features
          </p>
        </div>

        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between max-w-2xl mx-auto">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={`flex items-center ${step < 3 ? 'flex-1' : ''}`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-colors ${
                    currentStep >= step
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {currentStep > step ? <CheckCircle className="w-5 h-5" /> : step}
                </div>
                {step < 3 && (
                  <div
                    className={`flex-1 h-1 mx-2 transition-colors ${
                      currentStep > step ? 'bg-primary' : 'bg-muted'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between max-w-2xl mx-auto mt-2">
            <span className="text-sm">Contact Info</span>
            <span className="text-sm">Business Details</span>
            <span className="text-sm">AI Summary</span>
          </div>
        </div>

        <Card className="shadow-xl">
          <CardContent className="p-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-4">Step 1: Contact Information</h2>
                  <p className="text-muted-foreground mb-6">
                    Enter your website URL to auto-fill company information using AI
                  </p>
                </div>

                <div className="grid gap-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Your Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="John Doe"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="+45 12 34 56 78"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website" className="flex items-center gap-2">
                      Website URL *
                      <Sparkles className="w-4 h-4 text-primary" />
                      <span className="text-xs text-muted-foreground">AI will analyze your website</span>
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        id="website"
                        value={formData.websiteUrl}
                        onChange={(e) => setFormData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                        placeholder="https://quickorder.io/dk/"
                        className="flex-1"
                      />
                      <Button
                        onClick={handleWebsiteAnalysis}
                        disabled={isAnalyzing || !formData.websiteUrl}
                        variant="secondary"
                      >
                        {isAnalyzing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <Globe className="mr-2 h-4 w-4" />
                            Analyze
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Show analysis results */}
                  {analysisResults && (
                    <Alert className="border-primary/20 bg-primary/5">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <AlertDescription>
                        <div className="space-y-2">
                          <p className="font-medium">Website analyzed successfully!</p>
                          <p className="text-sm">
                            Found: {analysisResults.analysis.company_profile.name} - 
                            {' '}{analysisResults.analysis.business_model.type} in {analysisResults.analysis.business_model.industry}
                          </p>
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => setShowAnalysisDetails(!showAnalysisDetails)}
                            className="p-0 h-auto"
                          >
                            {showAnalysisDetails ? 'Hide' : 'Show'} detailed analysis
                          </Button>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  {showAnalysisDetails && analysisResults && (
                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <Tabs defaultValue="profile" className="w-full">
                          <TabsList className="grid grid-cols-4 w-full">
                            <TabsTrigger value="profile">Profile</TabsTrigger>
                            <TabsTrigger value="marketing">Marketing</TabsTrigger>
                            <TabsTrigger value="digital">Digital</TabsTrigger>
                            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
                          </TabsList>
                          <TabsContent value="profile" className="space-y-2 mt-4">
                            <h4 className="font-semibold">Company Profile</h4>
                            <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-48">
                              {JSON.stringify(analysisResults.analysis.company_profile, null, 2)}
                            </pre>
                          </TabsContent>
                          <TabsContent value="marketing" className="space-y-2 mt-4">
                            <h4 className="font-semibold">Marketing Insights</h4>
                            <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-48">
                              {JSON.stringify(analysisResults.analysis.marketing_insights, null, 2)}
                            </pre>
                          </TabsContent>
                          <TabsContent value="digital" className="space-y-2 mt-4">
                            <h4 className="font-semibold">Digital Presence</h4>
                            <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-48">
                              {JSON.stringify(analysisResults.analysis.digital_presence, null, 2)}
                            </pre>
                          </TabsContent>
                          <TabsContent value="opportunities" className="space-y-2 mt-4">
                            <h4 className="font-semibold">Growth Opportunities</h4>
                            <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-48">
                              {JSON.stringify(analysisResults.analysis.pain_points_opportunities, null, 2)}
                            </pre>
                          </TabsContent>
                        </Tabs>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {/* Step 2: Business Details */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-4">Step 2: Business Details</h2>
                  <p className="text-muted-foreground mb-6">
                    These fields have been auto-filled based on your website analysis
                  </p>
                </div>

                <div className="grid gap-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company">
                        Company Name
                        {analysisResults && <span className="text-xs text-primary ml-2">(AI auto-filled)</span>}
                      </Label>
                      <Input
                        id="company"
                        value={formData.companyName}
                        onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                        placeholder="Company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="industry">
                        Industry
                        {analysisResults && <span className="text-xs text-primary ml-2">(AI auto-filled)</span>}
                      </Label>
                      <Input
                        id="industry"
                        value={formData.industry}
                        onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                        placeholder="Industry"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="challenges">Current Marketing Challenges</Label>
                    <Textarea
                      id="challenges"
                      value={formData.challenges}
                      onChange={(e) => setFormData(prev => ({ ...prev, challenges: e.target.value }))}
                      placeholder="What marketing challenges are you facing?"
                      rows={4}
                    />
                    {analysisResults && (
                      <p className="text-xs text-muted-foreground">
                        AI identified potential challenges: {analysisResults.analysis.pain_points_opportunities.potential_challenges.slice(0, 2).join(', ')}...
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="goals">Goals for Marketing Therapy</Label>
                    <Textarea
                      id="goals"
                      value={formData.goals}
                      onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}
                      placeholder="What would you like to achieve?"
                      rows={4}
                    />
                    {analysisResults && (
                      <p className="text-xs text-muted-foreground">
                        AI suggests focusing on: {analysisResults.analysis.pain_points_opportunities.growth_opportunities.slice(0, 2).join(', ')}...
                      </p>
                    )}
                  </div>

                  {analysisResults && (
                    <Alert>
                      <Sparkles className="h-4 w-4" />
                      <AlertDescription>
                        <strong>AI Insight:</strong> {analysisResults.executiveSummary.split('\n')[0]}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: AI Summary */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-4">Step 3: Review AI Summary</h2>
                  <p className="text-muted-foreground mb-6">
                    AI has generated a comprehensive summary. You can edit it before submission.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="border rounded-lg p-4 min-h-[400px]">
                    <EditorContent 
                      editor={editor} 
                      className="prose prose-sm max-w-none"
                    />
                  </div>

                  <Alert>
                    <Sparkles className="h-4 w-4" />
                    <AlertDescription>
                      This AI-generated summary will be sent to Asger to help prepare for your marketing therapy session.
                      Feel free to edit or add any additional information.
                    </AlertDescription>
                  </Alert>

                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-semibold mb-2">What happens next?</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Your booking request will be sent to Asger</li>
                      <li>• You'll receive a confirmation email within 24 hours</li>
                      <li>• Asger will review your AI summary to prepare for the session</li>
                      <li>• You'll be redirected to payment after confirmation</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                Previous
              </Button>
              
              {currentStep < 3 ? (
                <Button
                  onClick={nextStep}
                  disabled={
                    (currentStep === 1 && (!formData.name || !formData.email || !formData.websiteUrl)) ||
                    (currentStep === 2 && (!formData.companyName || !formData.industry))
                  }
                >
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    toast.success('Form submitted successfully! (This is a test - no data was actually sent)');
                    console.log('Final form data:', formData);
                    console.log('AI Analysis:', analysisResults);
                  }}
                  className="bg-primary"
                >
                  Submit Booking Request
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Debug panel */}
        <Card className="mt-8 bg-muted/50">
          <CardHeader>
            <CardTitle className="text-lg">Debug Information</CardTitle>
            <CardDescription>Current form state and AI analysis data</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="formdata">
              <TabsList>
                <TabsTrigger value="formdata">Form Data</TabsTrigger>
                <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
              </TabsList>
              <TabsContent value="formdata">
                <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-64">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              </TabsContent>
              <TabsContent value="analysis">
                <pre className="text-xs bg-background p-3 rounded overflow-auto max-h-64">
                  {JSON.stringify(analysisResults, null, 2)}
                </pre>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestComprehensiveAnalysis;