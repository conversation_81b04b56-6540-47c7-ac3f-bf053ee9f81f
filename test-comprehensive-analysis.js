// Test script for comprehensive website analysis
// Run with: node test-comprehensive-analysis.js

import fetch from 'node-fetch';
const testUrl = 'https://quickorder.io/dk/';

async function testComprehensiveAnalysis() {
  console.log('Testing comprehensive analysis for:', testUrl);
  console.log('-----------------------------------\n');

  try {
    // Test the comprehensive analysis endpoint
    const response = await fetch('http://localhost:5173/api/analyze-website-comprehensive', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: testUrl,
        formData: {
          companyName: 'QuickOrder',
          industry: 'Technology'
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    console.log('SUCCESS! Comprehensive analysis completed\n');
    
    // Display executive summary
    if (data.executiveSummary) {
      console.log('EXECUTIVE SUMMARY:');
      console.log('==================');
      console.log(data.executiveSummary);
      console.log('\n');
    }
    
    // Display key findings
    if (data.analysis) {
      console.log('KEY FINDINGS:');
      console.log('=============\n');
      
      // Company Profile
      if (data.analysis.company_profile) {
        console.log('Company Profile:');
        console.log('- Name:', data.analysis.company_profile.name);
        console.log('- Legal Name:', data.analysis.company_profile.legal_name);
        console.log('- Size:', data.analysis.company_profile.size);
        console.log('- Locations:', data.analysis.company_profile.locations?.join(', ') || 'N/A');
        console.log('');
      }
      
      // Business Model
      if (data.analysis.business_model) {
        console.log('Business Model:');
        console.log('- Type:', data.analysis.business_model.type);
        console.log('- Industry:', data.analysis.business_model.industry);
        console.log('- Revenue Model:', data.analysis.business_model.revenue_model);
        console.log('- Target Markets:', data.analysis.business_model.target_markets?.join(', ') || 'N/A');
        console.log('- Customer Segments:', data.analysis.business_model.customer_segments?.join(', ') || 'N/A');
        console.log('');
      }
      
      // Products & Services
      if (data.analysis.products_services) {
        console.log('Products & Services:');
        if (data.analysis.products_services.main_offerings?.length > 0) {
          console.log('- Main Offerings:');
          data.analysis.products_services.main_offerings.forEach(offering => {
            console.log('  •', offering);
          });
        }
        console.log('- Pricing Model:', data.analysis.products_services.pricing_model || 'N/A');
        if (data.analysis.products_services.unique_selling_points?.length > 0) {
          console.log('- USPs:');
          data.analysis.products_services.unique_selling_points.forEach(usp => {
            console.log('  •', usp);
          });
        }
        console.log('');
      }
      
      // Marketing Insights
      if (data.analysis.marketing_insights) {
        console.log('Marketing Insights:');
        console.log('- Value Proposition:', data.analysis.marketing_insights.value_proposition);
        console.log('- Brand Positioning:', data.analysis.marketing_insights.brand_positioning);
        console.log('- Tone of Voice:', data.analysis.marketing_insights.tone_of_voice);
        if (data.analysis.marketing_insights.marketing_channels?.length > 0) {
          console.log('- Marketing Channels:', data.analysis.marketing_insights.marketing_channels.join(', '));
        }
        console.log('');
      }
      
      // Digital Presence
      if (data.analysis.digital_presence) {
        console.log('Digital Presence:');
        console.log('- Website Quality:', data.analysis.digital_presence.website_quality);
        console.log('- Mobile Optimized:', data.analysis.digital_presence.mobile_optimized);
        console.log('- Languages:', data.analysis.digital_presence.languages?.join(', ') || 'N/A');
        console.log('- Blog Active:', data.analysis.digital_presence.blog_active);
        console.log('- Newsletter:', data.analysis.digital_presence.newsletter);
        console.log('');
      }
      
      // Pain Points & Opportunities
      if (data.analysis.pain_points_opportunities) {
        console.log('Pain Points & Opportunities:');
        if (data.analysis.pain_points_opportunities.problems_they_solve?.length > 0) {
          console.log('- Problems They Solve:');
          data.analysis.pain_points_opportunities.problems_they_solve.forEach(problem => {
            console.log('  •', problem);
          });
        }
        if (data.analysis.pain_points_opportunities.growth_opportunities?.length > 0) {
          console.log('- Growth Opportunities:');
          data.analysis.pain_points_opportunities.growth_opportunities.forEach(opp => {
            console.log('  •', opp);
          });
        }
        console.log('');
      }
    }
    
    // Display additional extracted data
    if (data.additionalData) {
      console.log('ADDITIONAL EXTRACTED DATA:');
      console.log('==========================');
      if (data.additionalData.emails?.length > 0) {
        console.log('- Emails:', data.additionalData.emails.join(', '));
      }
      if (data.additionalData.phoneNumbers?.length > 0) {
        console.log('- Phone Numbers:', data.additionalData.phoneNumbers.join(', '));
      }
      if (data.additionalData.socialMedia && Object.keys(data.additionalData.socialMedia).length > 0) {
        console.log('- Social Media:');
        for (const [platform, url] of Object.entries(data.additionalData.socialMedia)) {
          console.log(`  • ${platform}: ${url}`);
        }
      }
      if (data.additionalData.cvr) {
        console.log('- CVR:', data.additionalData.cvr);
      }
      if (data.additionalData.visiblePrices?.length > 0) {
        console.log('- Visible Prices:', data.additionalData.visiblePrices.join(', '));
      }
      console.log('');
    }
    
    // Save full response for debugging
    const fs = require('fs');
    fs.writeFileSync('comprehensive-analysis-result.json', JSON.stringify(data, null, 2));
    console.log('Full analysis saved to: comprehensive-analysis-result.json');
    
  } catch (error) {
    console.error('ERROR:', error.message);
    console.error('\nMake sure:');
    console.error('1. The development server is running (npm run dev)');
    console.error('2. You have FIRECRAWL_API_KEY and OPENAI_API_KEY in your .env file');
    console.error('3. The API endpoints are properly configured');
  }
}

// Run the test
testComprehensiveAnalysis();