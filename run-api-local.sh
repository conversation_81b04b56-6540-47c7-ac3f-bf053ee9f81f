#!/bin/bash

# Kill any existing servers
pkill -f "tsx.*local-api-server" 2>/dev/null
pkill -f "vite" 2>/dev/null

echo "Starting API server..."

# Start API server in background
npx tsx local-api-server.ts &
API_PID=$!

# Wait for API server to start
sleep 3

# Test if API is running
echo "Testing API health..."
curl -s http://localhost:3000/api/health && echo "" || echo "API health check failed"

# Start Vite
echo ""
echo "Starting Vite dev server..."
npm run dev

# Cleanup on exit
trap "kill $API_PID 2>/dev/null" EXIT