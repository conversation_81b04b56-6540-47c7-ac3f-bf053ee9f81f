{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "functions": {"api/**/*.ts": {"maxDuration": 10}}, "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/about", "destination": "/index.html"}, {"source": "/services", "destination": "/index.html"}, {"source": "/services/(.*)", "destination": "/index.html"}, {"source": "/contact", "destination": "/index.html"}, {"source": "/booking", "destination": "/index.html"}, {"source": "/booking/(.*)", "destination": "/index.html"}, {"source": "/experience", "destination": "/index.html"}, {"source": "/success", "destination": "/index.html"}, {"source": "/request-success", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), interest-cohort=()"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://eu.i.posthog.com https://eu-assets.i.posthog.com https://fonts.googleapis.com https://cdn.gpteng.co https://app.cal.com https://calendar.google.com https://*.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://calendar.google.com https://*.google.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://eu.i.posthog.com https://eu-assets.i.posthog.com https://api.stripe.com https://calendar.google.com https://*.google.com; frame-src 'self' https://calendar.google.com https://*.google.com https://app.cal.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/assets/(.*).css", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*).js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}