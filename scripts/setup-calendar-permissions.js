// Script to test and setup Google Calendar permissions
// Run this locally after setting up your service account

const { google } = require('@googleapis/calendar');
const fs = require('fs');

async function setupCalendarPermissions() {
  try {
    // Load service account credentials
    // Replace with path to your downloaded JSON key file
    const credentials = JSON.parse(fs.readFileSync('./service-account-key.json', 'utf8'));
    
    const auth = new google.auth.GoogleAuth({
      credentials: credentials,
      scopes: ['https://www.googleapis.com/auth/calendar'],
    });

    const calendar = google.calendar({ version: 'v3', auth });

    // Test by listing calendars
    console.log('Testing calendar access...');
    const calendarList = await calendar.calendarList.list();
    console.log('Accessible calendars:', calendarList.data.items?.map(cal => ({
      id: cal.id,
      summary: cal.summary,
      accessRole: cal.accessRole
    })));

    // Test creating an event (then immediately delete it)
    const testEvent = {
      summary: 'Test Event - Delete Me',
      start: { dateTime: new Date(Date.now() + ********).toISOString() },
      end: { dateTime: new Date(Date.now() + ********).toISOString() },
    };

    console.log('\nTesting event creation...');
    const createdEvent = await calendar.events.insert({
      calendarId: 'primary', // or your specific calendar ID
      requestBody: testEvent,
    });
    console.log('✓ Successfully created test event');

    // Clean up - delete test event
    await calendar.events.delete({
      calendarId: 'primary',
      eventId: createdEvent.data.id,
    });
    console.log('✓ Successfully deleted test event');
    console.log('\nCalendar API access is working correctly!');

  } catch (error) {
    console.error('Error:', error.message);
    if (error.code === 403) {
      console.log('\n❌ Permission denied. Please ensure:');
      console.log('1. The calendar is shared with the service account');
      console.log('2. The service account has "Make changes to events" permission');
      console.log('3. If using Google Workspace, domain policies allow external access');
    }
  }
}

// Run the test
setupCalendarPermissions();