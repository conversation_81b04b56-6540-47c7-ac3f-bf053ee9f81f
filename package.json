{"name": "asger-me-personal-brand", "private": true, "version": "1.0.1", "type": "module", "description": "Asger.me - Personal brand website serving as both online resume and marketing services platform", "author": "<PERSON><PERSON>", "homepage": "https://asger.me", "scripts": {"dev": "vite", "dev:api": "vercel dev --listen 3000", "dev:api-local": "node dev-server.js", "dev:api-tsx": "tsx watch local-api-server.ts", "dev:full": "concurrently \"npm run dev:api\" \"npm run dev\"", "dev:local": "concurrently \"npm run dev:api-local\" \"npm run dev\"", "dev:real": "concurrently \"npm run dev:api-tsx\" \"npm run dev\"", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@googleapis/calendar": "^11.0.1", "@hookform/resolvers": "^3.9.0", "@mendable/firecrawl-js": "^1.29.3", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.5", "@sentry/react": "^9.42.1", "@sentry/tracing": "^7.120.3", "@stripe/stripe-js": "^7.6.1", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@vercel/node": "^5.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.12", "googleapis": "^155.0.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "openai": "^4.20.1", "posthog-js": "^1.258.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-i18next": "^15.6.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "stripe": "^18.3.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "web-vitals": "^5.0.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/cors": "^2.8.19", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-i18next": "^7.8.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "express": "^5.1.0", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.11", "tsx": "^4.20.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vercel": "^44.6.5", "vite": "^5.4.1", "vite-plugin-pwa": "^1.0.2", "workbox-window": "^7.3.0"}}