# Development Environment Variables Example
# Copy this to .env and update with your values

# App Configuration
VITE_APP_URL=http://localhost:8080
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Asger.me

# Analytics
VITE_PUBLIC_POSTHOG_KEY=phc_your_project_api_key
VITE_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com

# Sentry Error Tracking (Optional)
VITE_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# AI Website Analysis (Optional)
OPENAI_API_KEY=sk-your-openai-api-key
FIRECRAWL_API_KEY=your-firecrawl-api-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_MARKETING_THERAPY_PRICE_ID=price_xxxxxxxxxxxxxx
VITE_STRIPE_PAYMENT_LINK=https://book.stripe.com/test_xxxxxx

# Google Calendar Configuration
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
GOOGLE_CALENDAR_ID=primary
VITE_GOOGLE_CALENDAR_APPOINTMENT_URL=https://calendar.google.com/calendar/appointments/schedules/YOUR_SCHEDULE_ID?gv=true

# Database Configuration
DATABASE_URL=************************************/database

# Email Configuration (Brevo)
BREVO_API_KEY=your-brevo-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Asger Marketing

# Slack Notifications (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Internal API Security
INTERNAL_API_KEY=your-random-internal-api-key