# Deployment Guide for Asger.me

## Pre-Deployment Checklist

### ✅ Environment Variables (Set in Vercel Dashboard)
- `DATABASE_URL` - Neon PostgreSQL connection string
- `VITE_PUBLIC_POSTHOG_KEY` - PostHog analytics key (already set)
- `VITE_PUBLIC_POSTHOG_HOST` - PostHog host URL (already set)
- `VITE_SENTRY_DSN` - Sentry error tracking DSN (optional)
- `NODE_ENV` - Set to "production" for production builds

### ✅ Database Setup (Neon)
1. Run the SQL schema from `src/integrations/neon/schema.sql` in your Neon database console
2. Verify tables are created:
   - `leads`
   - `contact_submissions`
   - `newsletter_subscriptions`
   - `analytics_events`

### ✅ Vercel Configuration
- `vercel.json` configured for SPA routing and security headers
- API routes in `/api` directory ready for serverless functions
- Build command: `npm run build`
- Output directory: `dist`

## Database Schema Setup

Copy and run this SQL in your Neon database console:

```sql
-- See src/integrations/neon/schema.sql for complete schema
-- Key tables:
-- - leads (booking form data)
-- - contact_submissions (contact form data)
-- - newsletter_subscriptions (email subscriptions)
-- - analytics_events (custom analytics)
```

## API Endpoints

After deployment, these endpoints will be available:

- `GET /api/health` - Health check and database status
- `POST /api/contact` - Contact form submissions
- `POST /api/booking` - Booking form submissions (create/update leads)
- `GET /api/booking?email=...` - Get lead by email
- `POST /api/newsletter` - Newsletter subscriptions

## Testing After Deployment

1. **Health Check**: Visit `/api/health` to verify database connection
2. **Contact Form**: Test contact form submission
3. **Booking Form**: Test booking form submission
4. **Language Switching**: Verify Danish/English switching works
5. **Analytics**: Check PostHog for event tracking
6. **Performance**: Run Lighthouse audit

## Performance Optimizations Included

- ✅ Code splitting and lazy loading
- ✅ Bundle optimization (React vendor, UI vendor, analytics chunks)
- ✅ Image optimization with lazy loading
- ✅ Web Vitals monitoring
- ✅ Service Worker for caching (PWA)
- ✅ Security headers
- ✅ SEO optimization with meta tags

## Monitoring

- **Analytics**: PostHog for user behavior and conversions
- **Errors**: Sentry for error tracking (if configured)
- **Performance**: Web Vitals sent to PostHog
- **Database**: Health check endpoint for monitoring

## Post-Deployment Tasks

1. Test all forms and functionality
2. Verify analytics tracking
3. Check database connections
4. Monitor error rates
5. Run performance audits
6. Test mobile responsiveness
7. Verify SEO meta tags

## Rollback Plan

If issues occur:
1. Revert to previous Git commit
2. Redeploy from Vercel dashboard
3. Check environment variables
4. Verify database connectivity

## Support

- Database: Neon PostgreSQL
- Hosting: Vercel
- Analytics: PostHog
- Error Tracking: Sentry (optional)
- CDN: Vercel Edge Network
