const express = require('express');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set environment variables for API functions
process.env.FIRECRAWL_API_KEY = process.env.FIRECRAWL_API_KEY || process.env.VITE_FIRECRAWL_API_KEY;
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;

const app = express();
app.use(express.json());

// CORS for local development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// Simple TypeScript transpilation for API files
const fs = require('fs');
const path = require('path');

// Load and execute TypeScript API handlers
const loadApiHandler = (apiPath) => {
  const tsContent = fs.readFileSync(path.join(__dirname, apiPath), 'utf8');
  
  // Remove TypeScript-specific syntax (very basic)
  let jsContent = tsContent
    .replace(/import\s+.*?from\s+['"]@vercel\/node['"];?/g, '')
    .replace(/import\s+type\s+.*?;/g, '')
    .replace(/:\s*VercelRequest/g, '')
    .replace(/:\s*VercelResponse/g, '')
    .replace(/export\s+default\s+async\s+function\s+handler/g, 'module.exports = async function handler')
    .replace(/import\s+(.*?)\s+from\s+['"](.*)['"];?/g, 'const $1 = require("$2");')
    .replace(/export\s+default/g, 'module.exports =');

  // Create a temporary module
  const tempModule = { exports: {} };
  const moduleFunc = new Function('module', 'exports', 'require', '__dirname', '__filename', jsContent);
  moduleFunc(tempModule, tempModule.exports, require, __dirname, apiPath);
  
  return tempModule.exports;
};

// Load API handlers
try {
  const analyzeWebsiteComprehensive = loadApiHandler('api/analyze-website-comprehensive.ts');
  const analyzeWebsite = loadApiHandler('api/analyze-website.ts');
  const websiteMetadata = loadApiHandler('api/website-metadata.ts');

  // Set up routes
  app.post('/api/analyze-website-comprehensive', analyzeWebsiteComprehensive);
  app.post('/api/analyze-website', analyzeWebsite);
  app.post('/api/website-metadata', websiteMetadata);
} catch (error) {
  console.error('Error loading API handlers:', error);
}

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.API_PORT || 3000;
app.listen(PORT, () => {
  console.log(`API server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /api/analyze-website-comprehensive');
  console.log('  POST /api/analyze-website');
  console.log('  POST /api/website-metadata');
  console.log('  GET  /api/health');
});